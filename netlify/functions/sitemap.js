// Netlify function to handle sitemap.xml requests
const { getLocaleConfig, getAllLocales } = require('../../src/config/domains');

// Define path mappings for all locales (matching the API route)
const pathMappings = {
  'attendance': {
    cs: '/online-dochazka',
    sk: '/online-dochadzka',
    en: '/attendance'
  },
  'bookings': {
    cs: '/online-rezervace',
    sk: '/online-rezervacie',
    en: '/bookings'
  },
  'meetings': {
    cs: '/planovac-schuzek',
    sk: '/planovac-stretnuti',
    en: '/meetings'
  },
  'pricing': {
    cs: '/tymbox-plus',
    sk: '/tymbox-plus',
    en: '/pricing'
  },
  'no_install': {
    cs: '/mobilni-dochazka-bez-instalace',
    sk: '/mobilna-dochadzka-bez-instalacie',
    en: '/no-install'
  },
  'why_free': {
    cs: '/online-dochazka-zdarma',
    sk: '/online-dochadzka-zadarmo',
    en: '/free-attendance'
  },
  'for_small': {
    cs: '/pro-male-firmy',
    sk: '/pre-male-firmy',
    en: '/for-small-business'
  },
  'simple': {
    cs: '/dochazka-bez-skoleni',
    sk: '/dochadzka-bez-skoleni',
    en: '/simple-attendance'
  },
  'faq': {
    cs: '/caste-dotazy',
    sk: '/caste-otazky',
    en: '/faq'
  },
  'policies': {
    cs: '/podminky-sluzby',
    sk: '/podmienky-sluzby',
    en: '/terms'
  },
  'privacy': {
    cs: '/ochrana-dat',
    sk: '/ochrana-dat',
    en: '/privacy'
  },
  'contact': {
    cs: '/kontakt',
    sk: '/kontakt',
    en: '/contact'
  }
};

exports.handler = async function(event, context) {
  // Get the sitemap type from query params or path
  const isSitemap0 = event.path === '/sitemap-0.xml' || 
                     (event.queryStringParameters && event.queryStringParameters.type === 'sitemap-0');
  
  // Base URL is always tymbox.cz now (single domain approach)
  const baseUrl = 'https://tymbox.cz';
  const supportedLocales = getAllLocales(); // ['cs', 'sk', 'en']
  
  console.log('[Sitemap Function] Using single domain approach with locales:', supportedLocales);
  
  return generateSitemapResponse(event, isSitemap0, baseUrl, supportedLocales);
};

// Helper function to generate sitemap response
function generateSitemapResponse(event, isSitemap0, baseUrl, supportedLocales) {
  // If this is the main sitemap.xml
  if (event.path === '/sitemap.xml' && !isSitemap0) {
    // Return the sitemap index with correct Content-Type
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600'
      },
      body: `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <sitemap>
    <loc>${baseUrl}/sitemap-0.xml</loc>
  </sitemap>
</sitemapindex>`
    };
  }
  
  // If this is sitemap-0.xml, generate a dynamic sitemap
  if (isSitemap0) {
    // List of all public pages to include in sitemap
    const pageKeys = [
      '', // Homepage
      'attendance',
      'bookings',
      'meetings',
      'pricing',
      'no_install',
      'why_free',
      'for_small',
      'simple',
      'faq',
      'policies',
      'privacy',
      'contact'
    ];
    
    // XML header
    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
    xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" ';
    xml += 'xmlns:xhtml="http://www.w3.org/1999/xhtml">\n';

    // For each page, generate URL entries for all locales
    for (const pageKey of pageKeys) {
      for (const locale of supportedLocales) {
        // Get localized path for current locale
        const pagePath = pageKey === '' ? '' : 
          (pathMappings[pageKey] && pathMappings[pageKey][locale]) || 
          `/${pageKey}`;
        
        const fullUrl = `${baseUrl}/${locale}${pagePath}`;

        // Start URL entry
        xml += '  <url>\n';
        xml += `    <loc>${fullUrl}</loc>\n`;
        
        // Add hreflang tags for all locales
        supportedLocales.forEach(altLocale => {
          const altPath = pageKey === '' ? '' : 
            (pathMappings[pageKey] && pathMappings[pageKey][altLocale]) || 
            `/${pageKey}`;
          const altUrl = `${baseUrl}/${altLocale}${altPath}`;
          
          // Get locale config for proper language codes
          const localeConfig = getLocaleConfig(altLocale);
          const langCode = localeConfig.language.toLowerCase();
          
          xml += `    <xhtml:link rel="alternate" hreflang="${langCode}" href="${altUrl}" />\n`;
        });
        
        // Add x-default pointing to Czech version
        const czechPath = pageKey === '' ? '' : 
          (pathMappings[pageKey] && pathMappings[pageKey]['cs']) || 
          `/${pageKey}`;
        xml += `    <xhtml:link rel="alternate" hreflang="x-default" href="${baseUrl}/cs${czechPath}" />\n`;
        
        // Add basic sitemap properties
        xml += `    <changefreq>${pageKey === '' ? 'daily' : 'weekly'}</changefreq>\n`;
        xml += `    <priority>${pageKey === '' ? '1.0' : '0.8'}</priority>\n`;

        // End URL entry
        xml += '  </url>\n';
      }
    }

    // XML footer
    xml += '</urlset>';
    
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600'
      },
      body: xml
    };
  }
  
  // Return 404 for other paths
  return {
    statusCode: 404,
    body: 'Not found'
  };
}