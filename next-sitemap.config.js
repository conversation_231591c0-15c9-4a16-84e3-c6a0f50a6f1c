/** @type {import('next-sitemap').IConfig} */

// Single-domain sitemap configuration
// This will generate sitemaps for all locales under www.tymbox.cz
const siteUrl = process.env.SITE_URL || 'https://www.tymbox.cz';

// Import domain configurations
const { BASE_URL, getAllLocales, getLocaleConfig } = require('./src/config/domains');
// Import centralized path mappings
const { pathMappings, getAllPageKeys } = require('./src/config/paths');

// All supported locales
const locales = getAllLocales();

// List of all public pages to include in sitemap
const publicPageKeys = getAllPageKeys();

module.exports = {
  siteUrl: siteUrl,
  
  generateRobotsTxt: true,
  robotsTxtOptions: {
    policies: [
      {
        userAgent: '*',
        allow: '/',
      },
    ],
    additionalSitemaps: [
      `${BASE_URL}/sitemap.xml`
    ],
  },
  
  // Note: exclude array removed as it's ineffective when transform returns null
  
  additionalPaths: async (config) => {
    const result = [];
    
    // Generate sitemap entries for each page in each locale
    locales.forEach(locale => {
      const localeConfig = getLocaleConfig(locale);
      
      publicPageKeys.forEach(pageKey => {
        const pagePath = pageKey === '' ? '' : 
          (pathMappings[pageKey] && pathMappings[pageKey][locale]) || 
          `/${pageKey}`;
        
        const pageUrl = `${BASE_URL}/${locale}${pagePath}`;
        
        // Create alternateRefs for all locales - pointing to correct localized URLs
        const alternateRefs = locales.map(altLocale => {
          const altConfig = getLocaleConfig(altLocale);
          const altPagePath = pageKey === '' ? '' : 
            (pathMappings[pageKey] && pathMappings[pageKey][altLocale]) || 
            `/${pageKey}`;
          return {
            href: `${BASE_URL}/${altLocale}${altPagePath}`,
            hreflang: altConfig.language,
          };
        });
        
        // Add x-default pointing to Czech version
        const czechPagePath = pageKey === '' ? '' : 
          (pathMappings[pageKey] && pathMappings[pageKey]['cs']) || 
          `/${pageKey}`;
        alternateRefs.push({
          href: `${BASE_URL}/cs${czechPagePath}`,
          hreflang: 'x-default',
        });
        
        result.push({
          loc: pageUrl,
          changefreq: pageKey === '' ? 'daily' : 'weekly',
          priority: pageKey === '' ? 1.0 : 0.8,
          alternateRefs,
        });
      });
    });
    
    return result;
  },
  
  transform: async (config, path) => {
    // Skip all paths - we handle everything in additionalPaths
    return null;
  },
  
  outDir: 'public',
}; 