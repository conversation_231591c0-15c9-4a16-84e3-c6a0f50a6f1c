# Netlify configuration
[build]
  command = "npm run build"
  publish = ".next"
  functions = "netlify/functions"

# Environment variables
[build.environment]
  NEXT_TELEMETRY_DISABLED = "1"

# Ensure proper content-type headers for sitemap files
[[headers]]
  for = "/sitemap*.xml"
  [headers.values]
    Content-Type = "application/xml"
    X-Content-Type-Options = "nosniff"
    Cache-Control = "public, max-age=3600"

# Direct sitemap.xml requests to our custom function for staging
[[redirects]]
  from = "/sitemap.xml"
  to = "/.netlify/functions/sitemap"
  status = 200
  force = true

# Direct sitemap-0.xml requests to our custom function for staging
[[redirects]]
  from = "/sitemap-0.xml"
  to = "/.netlify/functions/sitemap?type=sitemap-0"
  status = 200
  force = true

# Enable functions
[functions]
  directory = "netlify/functions"
  node_bundler = "esbuild"

# Standard Next.js configuration for Netlify
[[plugins]]
  package = "@netlify/plugin-nextjs"