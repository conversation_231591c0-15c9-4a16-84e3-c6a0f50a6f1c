# Troubleshooting URL Mappings

**Date**: July 16, 2025  
**Related**: `02-seo-consistency-implementation.md`, `../locales/url-rewrite-implementation.md`

## Quick Diagnostic Commands

### 1. Check Generated Rewrites
```bash
node -e "
const { generateRewrites } = require('./src/config/paths');
const rewrites = generateRewrites();
console.log('Total rewrites:', rewrites.length);
rewrites.forEach(r => console.log(\`\${r.source} → \${r.destination}\`));
"
```

### 2. Test Specific URL
```bash
# Start dev server
npm run dev

# Test URL (in another terminal)
curl -I http://localhost:3000/cs/your-problematic-url

# Expected: HTTP/1.1 200 OK
# Problem: HTTP/1.1 404 Not Found
```

### 3. Verify Page Directory Structure
```bash
# List all page directories
ls -la src/app/[locale]/ | grep -E '^d'

# Check specific page exists
ls -la src/app/[locale]/your-page-key/
```

## Common Issues & Solutions

### Issue: 404 Error on Valid URLs

#### Symptoms
- URL returns 404 Not Found
- Page works in one locale but not others
- Browser shows "Page Not Found" from Next.js

#### Root Cause Analysis
```bash
# 1. Check the rewrite destination
node -e "
const { generateRewrites } = require('./src/config/paths');
const rewrites = generateRewrites();
const problem = rewrites.find(r => r.source === '/cs/your-url');
console.log('Rewrite:', problem);
"

# 2. Check if destination directory exists
ls -la src/app/[locale]/destination-page-key/

# 3. Verify page key matches directory name
```

#### Solution
Ensure page key in `pathMappings` matches actual directory:

```javascript
// ❌ WRONG - page key doesn't match directory
'attendance': {
  cs: '/online-dochazka'  // rewrites to /cs/attendance (directory: dochazka)
}

// ✅ CORRECT - page key matches directory
'dochazka': {
  cs: '/online-dochazka'  // rewrites to /cs/dochazka (directory: dochazka)
}
```

### Issue: Build Failures

#### Symptoms
- `npm run build` fails
- TypeScript errors about missing properties
- Module import errors

#### Common Causes & Fixes

**1. Missing page.tsx files**
```bash
# Check all required page files exist
find src/app/[locale] -name "page.tsx" -o -name "route.ts"
```

**2. TypeScript errors in route handlers**
```typescript
// Fix type issues in registration routes
const localeConfig = getLocaleConfig(locale);
const url = (localeConfig as any).registrationUrl; // Type assertion
```

**3. Import path errors**
```javascript
// Ensure correct imports
const { generateRewrites } = require('./src/config/paths'); // ✅
const { generateRewrites } = require('../config/paths');    // ❌
```

### Issue: Sitemap Generation Problems

#### Symptoms
- Sitemap contains wrong URLs
- Missing locale variants
- Hreflang links point to wrong pages

#### Diagnostic Commands
```bash
# Check sitemap generation
npm run postbuild

# Verify sitemap content
cat public/sitemap-0.xml | grep -A1 -B1 "problem-url"

# Test path mappings function
node -e "
const { pathMappings, getAllPageKeys } = require('./src/config/paths');
console.log('Page keys:', getAllPageKeys());
console.log('Attendance mapping:', pathMappings.dochazka);
"
```

#### Solution
Ensure `pathMappings` keys are updated in all related files and sitemap regeneration uses correct configuration.

### Issue: Inconsistent URLs Across System

#### Symptoms
- Navigation links work but sitemap URLs don't
- Different URLs for same page in different places
- Canonical URLs don't match actual URLs

#### Root Cause
Multiple sources of truth for URL mappings.

#### Solution Verification
```bash
# Ensure single source of truth
grep -r "online-dochazka" src/ --exclude-dir=node_modules

# Should only appear in:
# - src/config/paths.js (configuration)
# - No other files should have hardcoded paths
```

## Development Workflow

### Adding New Pages
1. **Create page directory**:
   ```bash
   mkdir -p src/app/[locale]/new-page
   echo "export default function NewPage() { return <div>New Page</div>; }" > src/app/[locale]/new-page/page.tsx
   ```

2. **Add to path mappings**:
   ```javascript
   // src/config/paths.js
   export const pathMappings = {
     // ... existing pages
     'new-page': {
       cs: '/nova-stranka',
       sk: '/nova-stranka', 
       en: '/new-page'
     }
   };
   ```

3. **Test all locales**:
   ```bash
   npm run dev
   curl -I http://localhost:3000/cs/nova-stranka    # Should be 200
   curl -I http://localhost:3000/sk/nova-stranka    # Should be 200
   curl -I http://localhost:3000/en/new-page        # Should be 200
   ```

4. **Verify build**:
   ```bash
   npm run build  # Should succeed without errors
   ```

### Modifying Existing Pages

1. **Check current configuration**:
   ```bash
   node -e "console.log(require('./src/config/paths').pathMappings['page-key'])"
   ```

2. **Update URLs** (if needed):
   ```javascript
   // Only change the SEO URLs, keep page key same
   'dochazka': {
     cs: '/new-czech-url',     // ✅ Change this
     sk: '/new-slovak-url',    // ✅ Change this
     en: '/new-english-url'    // ✅ Change this
   }
   // ❌ Don't change 'dochazka' key unless renaming directory
   ```

3. **Test changes**:
   ```bash
   npm run dev
   # Test old URLs return 404
   # Test new URLs return 200
   ```

## Emergency Fixes

### Quick 404 Fix
If a critical page is showing 404 in production:

1. **Identify the mismatch**:
   ```bash
   # Find the problematic rewrite
   node -e "
   const { generateRewrites } = require('./src/config/paths');
   console.log(generateRewrites().find(r => r.source.includes('problem-url')));
   "
   ```

2. **Check actual directory**:
   ```bash
   ls src/app/[locale]/ | grep -i "keyword"
   ```

3. **Fix page key**:
   ```javascript
   // Change page key to match directory name
   'correct-directory-name': {  // ← Must match actual folder
     cs: '/seo-url',
     // ...
   }
   ```

4. **Deploy immediately**:
   ```bash
   npm run build  # Verify fix
   git add . && git commit -m "Fix URL mapping for critical page"
   ```

### Rollback Strategy
If URL changes break the site:

1. **Revert path mappings**:
   ```bash
   git checkout HEAD~1 -- src/config/paths.js
   ```

2. **Test locally**:
   ```bash
   npm run build && npm run dev
   ```

3. **Deploy rollback**:
   ```bash
   git add . && git commit -m "Rollback URL mappings"
   ```

## Performance Monitoring

### Build Time Checks
- Rewrite generation should be fast (< 1 second)
- Sitemap generation should complete without errors
- No circular dependencies in path configurations

### Runtime Checks
- All navigation URLs return 200 OK
- No broken internal links
- Proper locale switching functionality

## Validation Checklist

Before deploying URL changes:

- [ ] All page keys match directory names
- [ ] Build completes successfully
- [ ] Sitemap generates without errors
- [ ] All locale variants return 200 OK
- [ ] Navigation links work correctly
- [ ] No broken internal references
- [ ] SEO canonical URLs are consistent

This troubleshooting guide should help quickly identify and resolve URL mapping issues in the multi-locale architecture.