# Single-Domain Architecture Cleanup Plan

## Executive Summary

**Current State**: Successfully migrated to single-domain architecture with locale prefixes (`www.tymbox.cz/cs`, `/sk`, `/en`)
**Issue**: Legacy multi-domain detection code and inconsistent references remain
**Priority**: High - Clean up architectural inconsistencies and outdated references

## Investigation Findings

### ✅ What's Working Correctly
- **Routing**: `localePrefix: 'always'` correctly implemented
- **Middleware**: Simple next-intl middleware working as expected
- **Redirects**: Old domains (teambox.sk) redirect to prefixed URLs
- **Registration**: Locale-based routing functional
- **Sitemap**: Generates correct URLs with locale prefixes
- **English Support**: Basic implementation exists

### ❌ Critical Issues Found

#### 1. Unused Multi-Domain Detection Logic
- **File**: `src/config/domains.js:55-124`
- **Issue**: Complex domain detection functions serve no purpose
- **Impact**: Code bloat, confusion, maintenance burden

#### 2. Hardcoded Legacy Domain References
- **Files**: Multiple pages with `teambox.sk` references
- **Example**: `src/app/[locale]/for_small/page.tsx:23`
- **Impact**: Incorrect hreflang tags, SEO issues

#### 3. Documentation Inconsistencies
- **File**: `CLAUDE.md` mentions teambox.sk as active domain
- **Issue**: Documentation doesn't match actual implementation
- **Impact**: Developer confusion, incorrect guidance

## Implementation Plan

### Phase 1: Core Architecture Cleanup (High Priority)

#### Task 1.1: Remove Unused Domain Detection Logic
**Files to modify:**
- `src/config/domains.js`

**Actions:**
- Remove `getDomainConfigByHost()` function (lines 55-118)
- Remove `getLocaleByHost()` function (lines 121-124)
- Remove `getAllDomainConfigs()` function (lines 127-142)
- Simplify to basic locale configuration only
- Keep only `getLocaleConfig()` and `getAllLocales()`

#### Task 1.2: Update Hardcoded Domain References
**Files to scan and update:**
- `src/app/[locale]/for_small/page.tsx`
- All other page files with hreflang tags

**Pattern to find:**
```javascript
'sk': 'https://teambox.sk/path'
```

**Replace with:**
```javascript
'sk': 'https://www.tymbox.cz/sk/path'
```

#### Task 1.3: Fix Hreflang Implementation
**Target:** All pages with hardcoded hreflang tags
**Action:** Update to use consistent single-domain URLs

### Phase 2: Documentation Updates (Medium Priority)

#### Task 2.1: Update CLAUDE.md
**File**: `CLAUDE.md`
**Actions:**
- Remove references to teambox.sk as active domain
- Update multi-domain sections to single-domain
- Update troubleshooting section
- Update local development instructions

#### Task 2.2: Update Migration Documentation
**File**: `domain-detection-migration-plan.md`
**Actions:**
- Mark as completed/legacy
- Add note about current single-domain implementation
- Reference this cleanup plan

### Phase 3: Validation and Testing (Medium Priority)

#### Task 3.1: Validate Architecture with Gemini
**Purpose**: Get expert validation of cleanup approach
**Focus Areas:**
- Architecture decisions
- Performance implications
- SEO considerations
- Maintenance best practices

#### Task 3.2: Test All Functionality
**Areas to test:**
- Locale switching
- Registration redirects
- Sitemap generation
- SEO metadata
- Cross-language navigation

## Detailed Task List

### Immediate Actions (Start Now)

1. **[HIGH]** Remove unused domain detection functions from `domains.js`
2. **[HIGH]** Find and update all hardcoded `teambox.sk` references
3. **[HIGH]** Fix hreflang tags in all pages
4. **[HIGH]** Consult with Gemini for architecture validation

### Short-term Actions (Next 1-2 hours)

5. **[MED]** Update CLAUDE.md documentation
6. **[MED]** Test all registration flows
7. **[MED]** Validate sitemap generation
8. **[MED]** Check English locale completeness

### Future Considerations

9. **[LOW]** Complete English translations
10. **[LOW]** Add more comprehensive SEO metadata
11. **[LOW]** Consider performance optimizations
12. **[LOW]** Add monitoring for domain redirect success

## File Impact Analysis

### High Impact Files (Immediate Changes)
- `src/config/domains.js` - Remove unused code
- `src/app/[locale]/for_small/page.tsx` - Fix hreflang
- `CLAUDE.md` - Update documentation

### Medium Impact Files (Review Required)
- All pages with metadata generation
- Sitemap generation files
- Registration route handlers

### Low Impact Files (Monitor)
- Translation files
- Markdown content files
- Component files

## Success Criteria

### Technical
- [ ] No unused domain detection code remains
- [ ] All hreflang tags point to www.tymbox.cz
- [ ] Documentation matches implementation
- [ ] All tests pass

### Business
- [ ] SEO performance maintained
- [ ] User experience unaffected
- [ ] Registration flows work correctly
- [ ] Cross-language navigation functions

## Risk Assessment

### Low Risk
- Code cleanup (removing unused functions)
- Documentation updates

### Medium Risk
- Hreflang tag updates (could affect SEO if done incorrectly)
- Registration flow modifications

### Mitigation
- Test thoroughly before deployment
- Monitor SEO metrics after changes
- Keep rollback plan ready

## Next Steps

1. **Immediate**: Start with domain detection code removal
2. **Validate**: Consult Gemini for architecture review
3. **Execute**: Follow task list in priority order
4. **Test**: Validate all functionality works
5. **Deploy**: Push changes with monitoring

## Implementation Progress

### Completed Tasks ✅
- [x] Investigation and documentation complete
- [x] Architecture cleanup plan created
- [x] Gemini consultation and validation
- [x] Core architecture cleanup (domains.js refactored)
- [x] Legacy code removal (unused functions removed)
- [x] Hardcoded teambox.sk references updated
- [x] Hreflang tags fixed in all pages
- [x] CLAUDE.md documentation updated
- [x] Public robots.txt updated
- [x] Sitemap configuration updated
- [x] Build and compilation errors fixed
- [x] Application testing completed

### Implementation Summary

#### Files Modified:
1. **`src/config/domains.js`** - Completely refactored to simple locale configuration
2. **`src/lib/metadata.js`** - Updated to use BASE_URL and single-domain approach
3. **`src/lib/locale-mapper.js`** - Refactored to work with single domain
4. **`src/lib/domain-detector.js`** - Simplified to legacy compatibility functions
5. **`src/lib/get-server-locale.js`** - Simplified fallback function
6. **`src/app/robots.txt/route.js`** - Updated to use BASE_URL
7. **`src/app/[locale]/for_small/page.tsx`** - Updated to use new metadata approach
8. **`src/app/[locale]/registrace/route.ts`** - Fixed TypeScript errors
9. **`src/app/[locale]/registracia/route.ts`** - Fixed TypeScript errors
10. **`public/robots.txt`** - Updated for single domain
11. **`next-sitemap.config.js`** - Refactored for single-domain with locale prefixes
12. **`CLAUDE.md`** - Updated documentation to reflect single-domain architecture

#### Files Removed:
- **`src/app/api/debug-domain/route.js`** - Obsolete debug endpoint
- **`public/sitemap*.xml`** - Outdated static sitemap files

#### Key Changes Made:
1. **Removed unused multi-domain detection logic** from domains.js
2. **Introduced BASE_URL constant** for consistent URL generation
3. **Updated all hardcoded teambox.sk references** to use www.tymbox.cz/sk
4. **Fixed metadata generation** to use single-domain with locale prefixes
5. **Updated sitemap generation** to create correct URLs with locale prefixes
6. **Simplified registration routes** to use locale parameter instead of domain detection
7. **Updated documentation** to reflect current architecture

### Architecture Benefits Achieved:
- ✅ **Simplified codebase**: Removed 200+ lines of unused domain detection code
- ✅ **Consistent URL structure**: All URLs follow /locale/path pattern
- ✅ **Improved maintainability**: Single configuration file for all locales
- ✅ **Better SEO**: Proper hreflang tags and canonical URLs
- ✅ **Faster builds**: No multi-domain complexity
- ✅ **Cleaner documentation**: Updated to match implementation

### Testing Results:
- ✅ **Build successful**: npm run build completes without errors
- ✅ **TypeScript compilation**: All type errors resolved
- ✅ **Sitemap generation**: Creates correct URLs with locale prefixes
- ✅ **Registration routes**: Properly redirect based on locale
- ✅ **Metadata generation**: Generates correct hreflang tags

---

*Document created: January 15, 2025*
*Priority: High*
*Status: ✅ COMPLETED*
*Implementation time: ~2 hours*