# SEO Consistency Implementation

**Date**: July 16, 2025  
**Status**: ✅ Completed  
**Priority**: Critical (P0)

## Overview

This document details the implementation of SEO consistency improvements that eliminated multiple sources of truth for URL mappings and resolved 404 errors in the single-domain architecture.

## Problem Statement

### Initial Issues
- **Multiple Sources of Truth**: URL path mappings were defined independently in multiple files
- **SEO Inconsistencies**: High risk of inconsistencies between sitemap, canonical URLs, and actual pages
- **404 Errors**: Mismatched page keys caused rewrite failures for key navigation URLs
- **Maintenance Burden**: Duplicate path definitions across configuration files

### Files Affected
- `next-sitemap.config.js` - Used keys like `'attendance'`
- `next.config.js` - Used destination paths like `'/cs/dochazka'`
- `src/lib/metadata.js` - Had its own `localizedPaths` object
- `src/lib/locale-mapper.js` - Duplicated path mapping logic

## Implementation

### 1. Centralized Path Configuration
**File**: `src/config/paths.js`

Created a single source of truth for all URL mappings:

```javascript
// ABOUTME: Centralized URL path mappings for all locales to ensure SEO consistency
// ABOUTME: Single source of truth for sitemap generation, rewrites, and canonical URLs

export const pathMappings = {
  'dochazka': {
    cs: '/online-dochazka',
    sk: '/online-dochadzka', 
    en: '/attendance'
  },
  'rezervace': {
    cs: '/online-rezervace',
    sk: '/online-rezervacie',
    en: '/bookings'
  },
  // ... all other pages
};

export const generateRewrites = () => {
  const rewrites = [];
  for (const [pageKey, locales] of Object.entries(pathMappings)) {
    for (const [locale, path] of Object.entries(locales)) {
      rewrites.push({
        source: `/${locale}${path}`,
        destination: `/${locale}/${pageKey}`,
      });
    }
  }
  return rewrites;
};
```

### 2. Updated Configuration Files

#### next.config.js
```javascript
const { generateRewrites } = require('./src/config/paths');

async rewrites() {
  return {
    beforeFiles: [
      // SEO-friendly URL rewrites generated from centralized config
      ...generateRewrites(),
    ],
    afterFiles: [],
    fallback: [],
  };
}
```

#### next-sitemap.config.js
```javascript
const { pathMappings, getAllPageKeys } = require('./src/config/paths');

const publicPageKeys = getAllPageKeys();
// Removed duplicate path definitions
// Fixed lastmod spam issue
```

#### src/lib/metadata.js
```javascript
import { pathMappings, getSeoPath } from '@/config/paths';

// Removed duplicate localizedPaths object
// Now uses centralized configuration
```

### 3. Critical Bug Fix: Page Key Alignment

**Root Cause**: Page keys in `pathMappings` didn't match actual Next.js directory names.

**Problem Examples**:
- `'attendance'` key → `/cs/attendance` destination → but page at `/dochazka/` → 404
- `'bookings'` key → `/cs/bookings` destination → but page at `/rezervace/` → 404

**Solution**: Updated page keys to match directory structure:
```javascript
// BEFORE (causing 404s)
'attendance': { cs: '/online-dochazka', ... }  // ❌ rewrites to /cs/attendance
'bookings': { cs: '/online-rezervace', ... }   // ❌ rewrites to /cs/bookings

// AFTER (working)
'dochazka': { cs: '/online-dochazka', ... }    // ✅ rewrites to /cs/dochazka
'rezervace': { cs: '/online-rezervace', ... }  // ✅ rewrites to /cs/rezervace
```

### 4. Removed Redundant Files
- **Deleted**: `src/lib/locale-mapper.js` (duplicate logic)
- **Fixed**: Registration routes now use `getLocaleConfig()` from domains.js
- **Updated**: `getAllLocales()` derives from config object instead of hardcoded array

## Results

### ✅ Critical Issues Resolved
- **Single Source of Truth**: All URL mappings now come from `src/config/paths.js`
- **No 404 Errors**: All navigation URLs work correctly
- **Consistent SEO**: Sitemap, canonical URLs, and rewrites are aligned
- **Eliminated Duplication**: Removed redundant path definitions

### ✅ Build & Testing
- Build succeeds without errors
- Sitemap generation completes successfully
- All URL rewrites work correctly
- Registration redirects function properly
- No TypeScript errors

### ✅ URL Verification
All previously broken URLs now return **HTTP 200 OK**:
- `/cs/online-dochazka` → ✅ Working
- `/cs/online-rezervace` → ✅ Working  
- `/cs/planovac-schuzek` → ✅ Working
- `/cs/tymbox-plus` → ✅ Working

## Architecture Benefits

### Maintainability
- **Single Configuration**: All path changes happen in one file
- **Type Safety**: Helper functions provide consistent access patterns
- **Documentation**: Clear comments explain the centralized approach

### SEO Optimization
- **Consistent URLs**: Perfect alignment between sitemap and actual pages
- **Proper Hreflang**: Cross-language linking structure maintained
- **Clean Sitemaps**: Removed lastmod spam and ineffective configurations

### Developer Experience
- **Predictable Structure**: Page keys match directory names
- **Easy Updates**: Adding new pages requires only one configuration change
- **Clear Debugging**: Centralized logic makes troubleshooting straightforward

## Future Considerations

### Adding New Pages
1. Create page directory: `src/app/[locale]/new-page/`
2. Add mapping to `pathMappings` in `src/config/paths.js`
3. Build automatically generates rewrites and sitemap entries

### Maintenance
- All URL changes should happen in `src/config/paths.js`
- Page keys MUST match actual directory names
- Test build and URL accessibility after changes

## Technical Debt Eliminated

- ❌ **Multiple path definitions** → ✅ Single source of truth
- ❌ **Hardcoded URL mappings** → ✅ Generated from configuration
- ❌ **Inconsistent sitemap/rewrites** → ✅ Perfect alignment
- ❌ **Manual maintenance** → ✅ Automated generation
- ❌ **404 errors** → ✅ All URLs working

This implementation provides a robust, maintainable foundation for the multi-locale URL structure while ensuring perfect SEO consistency across all pages.