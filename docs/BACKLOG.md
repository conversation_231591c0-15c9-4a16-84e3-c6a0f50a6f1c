# Development Backlog - Tymbox Landing Page

## Overview
This backlog contains prioritized improvements identified during the comprehensive code review following the single-domain architecture cleanup. All items are documented with detailed implementation steps for future development.

**Generated**: January 15, 2025  
**Review Source**: Gemini Full Code Review  
**Architecture**: Single-domain with locale prefixes (`www.tymbox.cz/cs`, `/sk`, `/en`)

---

## 🔴 CRITICAL PRIORITY (P0) - Implement Immediately

### 1. **Multiple Sources of Truth for URL Mappings**
**Status**: 🔴 Critical  
**Impact**: High risk of inconsistencies between sitemap, canonical URLs, and actual pages  
**Effort**: 4-6 hours  
**Files Affected**: `next-sitemap.config.js`, `next.config.js`, `src/lib/metadata.js`, `src/lib/locale-mapper.js`

#### Problem Description
URL path mappings are defined independently in multiple files:
- `next-sitemap.config.js` uses keys like `'attendance'`
- `next.config.js` uses destination paths like `'/cs/dochazka'`
- `metadata.js` has its own `localizedPaths` object
- `locale-mapper.js` duplicates path mapping logic

This creates maintenance hell and risks SEO inconsistencies.

#### Implementation Steps
1. **Create centralized path mapping file**:
   ```javascript
   // src/config/paths.js
   export const pathMappings = {
     // Use neutral keys for internal page routes
     'attendance': {
       cs: '/online-dochazka',
       sk: '/online-dochadzka',
       en: '/attendance'
     },
     'bookings': {
       cs: '/online-rezervace',
       sk: '/online-rezervacie',
       en: '/bookings'
     },
     'meetings': {
       cs: '/planovac-schuzek',
       sk: '/planovac-stretnuti',
       en: '/meetings'
     },
     'pricing': {
       cs: '/tymbox-plus',
       sk: '/tymbox-plus',
       en: '/pricing'
     },
     'no_install': {
       cs: '/mobilni-dochazka-bez-instalace',
       sk: '/mobilna-dochadzka-bez-instalacie',
       en: '/no-install'
     },
     'why_free': {
       cs: '/online-dochazka-zdarma',
       sk: '/online-dochadzka-zadarmo',
       en: '/free-attendance'
     },
     'for_small': {
       cs: '/pro-male-firmy',
       sk: '/pre-male-firmy',
       en: '/for-small-business'
     },
     'simple': {
       cs: '/dochazka-bez-skoleni',
       sk: '/dochadzka-bez-skoleni',
       en: '/simple-attendance'
     },
     'faq': {
       cs: '/caste-dotazy',
       sk: '/caste-otazky',
       en: '/faq'
     },
     'policies': {
       cs: '/podminky-sluzby',
       sk: '/podmienky-sluzby',
       en: '/terms'
     },
     'privacy': {
       cs: '/ochrana-dat',
       sk: '/ochrana-dat',
       en: '/privacy'
     },
     'contact': {
       cs: '/kontakt',
       sk: '/kontakt',
       en: '/contact'
     }
   };

   // Helper to generate rewrite rules from the mappings
   export const generateRewrites = () => {
     const rewrites = [];
     for (const [pageKey, locales] of Object.entries(pathMappings)) {
       for (const [locale, path] of Object.entries(locales)) {
         rewrites.push({
           source: `/${locale}${path}`,
           destination: `/${locale}/${pageKey}`,
         });
       }
     }
     return rewrites;
   };
   ```

2. **Update `next.config.js`**:
   ```javascript
   const { generateRewrites } = require('./src/config/paths');
   
   async rewrites() {
     return {
       beforeFiles: [
         // Sitemap routes
         {
           source: '/sitemap.xml',
           destination: '/api/sitemap',
         },
         {
           source: '/sitemap-0.xml',
           destination: '/api/sitemap',
         },
         
         // Generated rewrites from central config
         ...generateRewrites(),
       ],
       afterFiles: [],
       fallback: [],
     };
   }
   ```

3. **Update `next-sitemap.config.js`**:
   ```javascript
   const { pathMappings } = require('./src/config/paths');
   
   const publicPageKeys = [
     '', // Homepage
     ...Object.keys(pathMappings)
   ];
   ```

4. **Update `src/lib/metadata.js`** to import from central config

5. **Remove redundant mapping logic** from other files

#### Acceptance Criteria
- [ ] All URL mappings come from single source
- [ ] Sitemap URLs match rewrite destinations
- [ ] Canonical URLs are consistent
- [ ] No duplicate path definitions
- [ ] Build succeeds without errors

---

### 2. **Sitemap Generation Strategy Conflict**
**Status**: 🔴 Critical  
**Impact**: Sitemap may not be served correctly  
**Effort**: 1-2 hours  
**Files Affected**: `next.config.js:70`, `next-sitemap.config.js:203`

#### Problem Description
`next-sitemap` generates static `sitemap.xml` in `/public`, but `next.config.js` has rewrite rules redirecting `/sitemap.xml` to `/api/sitemap`. Static files take precedence, making the rewrite ineffective.

#### Implementation Steps
1. **Choose one strategy**: Recommend using `next-sitemap` static generation
2. **Remove conflicting rewrites** from `next.config.js`:
   ```javascript
   // REMOVE THESE LINES
   // {
   //   source: '/sitemap.xml',
   //   destination: '/api/sitemap',
   // },
   // {
   //   source: '/sitemap-0.xml',
   //   destination: '/api/sitemap',
   // },
   ```

3. **Ensure `next-sitemap` runs in post-build**: Already configured in `package.json`

#### Acceptance Criteria
- [ ] Sitemap is served correctly at `/sitemap.xml`
- [ ] No conflicting routes
- [ ] Sitemap contains all expected URLs
- [ ] Build process includes sitemap generation

---

### 3. **Inconsistent Internal Page Routing**
**Status**: 🔴 Critical  
**Impact**: Confusing file structure and maintenance issues  
**Effort**: 6-8 hours  
**Files Affected**: `next.config.js:80-197`, All page files

#### Problem Description
English URLs like `/en/attendance` map to Czech-named internal paths like `/en/dochazka`, making the file structure non-intuitive.

#### Implementation Steps
1. **Rename page directories** to use neutral English keys:
   - `src/app/[locale]/dochazka/` → `src/app/[locale]/attendance/`
   - `src/app/[locale]/rezervace/` → `src/app/[locale]/bookings/`
   - `src/app/[locale]/schuzky/` → `src/app/[locale]/meetings/`
   - `src/app/[locale]/cenik/` → `src/app/[locale]/pricing/`
   - Continue for all pages...

2. **Update rewrite destinations** in `next.config.js` (or use `generateRewrites` helper)

3. **Update all internal imports** and references

4. **Test all routes** work correctly

#### Acceptance Criteria
- [ ] All page directories use neutral English names
- [ ] All routes work correctly
- [ ] No broken internal links
- [ ] File structure is intuitive
- [ ] All rewrites point to correct destinations

---

## 🟠 HIGH PRIORITY (P1) - Next Sprint

### 4. **DRY Principle Violation for Registration URLs**
**Status**: 🟠 High  
**Impact**: Code duplication and maintenance issues  
**Effort**: 1 hour  
**Files Affected**: `src/app/[locale]/registrace/route.ts`, `src/config/domains.js`

#### Problem Description
Registration URLs are hardcoded in route handlers but also defined in `domains.js`.

#### Implementation Steps
```typescript
// src/app/[locale]/registrace/route.ts
import { redirect } from 'next/navigation';
import { getLocaleConfig } from '@/config/domains';

export async function GET(
  request: Request,
  { params }: { params: { locale: string } }
) {
  const locale = params.locale;
  const localeConfig = getLocaleConfig(locale);
  const url = localeConfig.registrationUrl;
  redirect(url);
}
```

Apply same pattern to `registracia/route.ts`.

#### Acceptance Criteria
- [ ] Registration URLs come from single source
- [ ] No hardcoded URLs in route handlers
- [ ] Both registration routes work correctly

---

### 5. **Inconsistent `lastmod` in Sitemap**
**Status**: 🟠 High  
**Impact**: SEO - may appear spammy to search engines  
**Effort**: 30 minutes  
**Files Affected**: `next-sitemap.config.js:180`

#### Problem Description
Using `new Date().toISOString()` for all pages signals to search engines that all pages were updated at build time.

#### Implementation Steps
Remove the `lastmod` property:
```javascript
result.push({
  loc: pageUrl,
  changefreq: pageKey === '' ? 'daily' : 'weekly',
  priority: pageKey === '' ? 1.0 : 0.8,
  // lastmod: new Date().toISOString(), // REMOVE THIS LINE
  alternateRefs,
});
```

#### Acceptance Criteria
- [ ] No `lastmod` in sitemap entries
- [ ] Sitemap validates correctly
- [ ] SEO impact is neutral/positive

---

## 🟡 MEDIUM PRIORITY (P2) - Future Sprint

### 6. **Redundant `locale-mapper.js` Utility**
**Status**: 🟡 Medium  
**Impact**: Code duplication and maintenance burden  
**Effort**: 2 hours  
**Files Affected**: `src/lib/locale-mapper.js`

#### Problem Description
This file duplicates path mapping logic after centralizing path mappings.

#### Implementation Steps
1. **Review all usages** of `locale-mapper.js`
2. **Migrate logic** to `src/lib/metadata.js` using central path config
3. **Remove file** and update imports
4. **Test all functionality** still works

#### Acceptance Criteria
- [ ] No duplicate path mapping logic
- [ ] All functionality preserved
- [ ] Cleaner codebase

---

### 7. **Ineffective `exclude` Array in Sitemap Config**
**Status**: 🟡 Medium  
**Impact**: Confusing code  
**Effort**: 15 minutes  
**Files Affected**: `next-sitemap.config.js:122`

#### Problem Description
The `exclude` array has no effect because `transform` returns `null` for all paths.

#### Implementation Steps
Remove the `exclude` array:
```javascript
module.exports = {
  // exclude: [ ... ], // REMOVE THIS ENTIRE ARRAY
  
  additionalPaths: async (config) => {
    // existing logic
  },
};
```

#### Acceptance Criteria
- [ ] No misleading configuration
- [ ] Code is cleaner

---

## 🟢 LOW PRIORITY (P3) - Maintenance

### 8. **Hardcoded Locales Array**
**Status**: 🟢 Low  
**Impact**: Minor code quality  
**Effort**: 5 minutes  
**Files Affected**: `src/config/domains.js:46`

#### Problem Description
`getAllLocales()` returns hardcoded array instead of deriving from `localeConfigs`.

#### Implementation Steps
```javascript
export const getAllLocales = () => {
  return Object.keys(localeConfigs);
};
```

#### Acceptance Criteria
- [ ] Locales derived from config object
- [ ] No hardcoded arrays

---

## 📋 Implementation Planning

### Sprint 1 (Critical Items)
- **Week 1**: Items 1-2 (Path mappings and sitemap conflict)
- **Week 2**: Item 3 (Page routing restructure)

### Sprint 2 (High Priority)
- **Week 3**: Items 4-5 (DRY violations and sitemap lastmod)

### Sprint 3 (Medium Priority)
- **Week 4**: Items 6-7 (Code cleanup)

### Sprint 4 (Low Priority)
- **Week 5**: Item 8 (Minor improvements)

## 🧪 Testing Requirements

Each item requires:
- [ ] Unit tests for new functions
- [ ] Integration tests for routing
- [ ] Manual testing of all locales
- [ ] SEO validation (sitemap, canonical URLs)
- [ ] Performance testing

## 📊 Success Metrics

- **Code Quality**: Reduced duplication, single source of truth
- **SEO Health**: Consistent URLs, proper hreflang tags
- **Maintainability**: Clear file structure, intuitive naming
- **Performance**: No regressions in build time or runtime
- **User Experience**: All routes work correctly across locales

---
