# ABOUTME: Simple test setup guide for MVP landing page with internationalization
# ABOUTME: Focuses on essential configuration and basic test examples

# Simple Test Setup

## 1. Install Dependencies

```bash
# Core testing tools
npm install --save-dev @playwright/test vitest @vitejs/plugin-react jsdom

# Testing utilities
npm install --save-dev @testing-library/react @testing-library/jest-dom

# Install browsers for E2E tests
npx playwright install
```

## 2. Configuration Files

### Vitest Config (`vitest.config.ts`)
```typescript
import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./tests/setup.ts'],
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
})
```

### Playwright Config (`playwright.config.ts`)
```typescript
import { defineConfig } from '@playwright/test'

export default defineConfig({
  testDir: './tests/e2e',
  use: {
    baseURL: 'http://localhost:3000',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
  ],
})
```

### Test Setup (`tests/setup.ts`)
```typescript
import '@testing-library/jest-dom'
```

## 3. Package.json Scripts

```json
{
  "scripts": {
    "test": "vitest",
    "test:e2e": "playwright test",
    "test:e2e:ui": "playwright test --ui"
  }
}
```

## 4. Basic Test Examples

### E2E Test - Pages Load
```javascript
// tests/e2e/pages.spec.js
import { test, expect } from '@playwright/test'

test.describe('Page Loading', () => {
  test('homepage loads in all locales', async ({ page }) => {
    // Test Czech
    await page.goto('/cs')
    await expect(page).toHaveTitle(/Tymbox/)
    
    // Test Slovak
    await page.goto('/sk')
    await expect(page).toHaveTitle(/Tymbox/)
    
    // Test English
    await page.goto('/en')
    await expect(page).toHaveTitle(/Tymbox/)
  })
  
  test('navigation works between locales', async ({ page }) => {
    await page.goto('/cs')
    
    // Click language switcher
    await page.click('[data-testid="lang-sk"]')
    await expect(page).toHaveURL(/\/sk/)
    
    await page.click('[data-testid="lang-en"]')
    await expect(page).toHaveURL(/\/en/)
  })
})
```

### Unit Test - Component
```javascript
// tests/unit/navigation.test.js
import { render } from '@testing-library/react'
import Navigation from '@/components/Navigation'

test('navigation renders correctly', () => {
  const { getByText } = render(<Navigation />)
  expect(getByText('Home')).toBeInTheDocument()
})
```

### E2E Test - Forms
```javascript
// tests/e2e/forms.spec.js
import { test, expect } from '@playwright/test'

test('contact form submits successfully', async ({ page }) => {
  await page.goto('/cs/kontakt')
  
  await page.fill('[name="name"]', 'Test User')
  await page.fill('[name="email"]', '<EMAIL>')
  await page.fill('[name="message"]', 'Test message')
  
  await page.click('[type="submit"]')
  
  await expect(page.locator('.success-message')).toBeVisible()
})
```

## 5. Running Tests

```bash
# Run unit tests
npm run test

# Run E2E tests
npm run test:e2e

# Run E2E tests with UI
npm run test:e2e:ui
```

## 6. What to Test

### ✅ Essential Tests
- [ ] Pages load in all locales (/cs, /sk, /en)
- [ ] Navigation works between locales
- [ ] Contact form submits successfully
- [ ] Registration links work correctly
- [ ] Basic SEO elements are present

### ⚠️ Don't Over-Test
- Skip testing third-party components
- Don't test implementation details
- Avoid testing styles unless critical
- Focus on user behavior, not code coverage

## 7. Test Data

Use data-testid attributes for stable selectors:

```jsx
// In your components
<button data-testid="submit-button">Submit</button>
<nav data-testid="main-navigation">...</nav>
```

```javascript
// In your tests
await page.click('[data-testid="submit-button"]')
```

## That's It!

Keep tests simple and focused on user experience. Add tests when you find bugs, not before.