# Simple Testing for MVP Landing Page

## Overview

Simple testing approach for an MVP landing page with internationalization. Focus on core functionality and user experience.

**Philosophy**: Test what matters, keep it simple  
**Tools**: <PERSON><PERSON> (E2E), <PERSON>itest (Unit), Manual testing

---

## 🎯 Quick Start

### Install
```bash
# Install testing dependencies
npm install --save-dev @playwright/test vitest @vitejs/plugin-react jsdom

# Install Playwright browsers
npx playwright install
```

### Run Tests
```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e
```

---

## 🧪 What We Test

### 🎯 Essential Tests Only

1. **Page Loading** - Pages load without errors in all locales
2. **Navigation** - Links work correctly between locales  
3. **Content Display** - Text shows in the correct language
4. **Forms** - Contact/registration forms submit properly
5. **SEO Basics** - Meta tags and basic SEO elements work

### 🛠️ Simple Tools

- **Playwright** - For end-to-end testing (simulates real user behavior)
- **Vitest** - For unit testing (tests individual components)
- **Manual Testing** - For complex scenarios

## Test Structure

```
tests/
├── e2e/            # End-to-end tests (user journeys)
├── unit/           # Component tests
└── utils/          # Test helpers
```

## Core Test Examples

### E2E Test - Page Loading
```javascript
// tests/e2e/pages.spec.js
test('homepage loads in all locales', async ({ page }) => {
  await page.goto('/cs');
  await expect(page).toHaveTitle(/Tymbox/);
  
  await page.goto('/sk');
  await expect(page).toHaveTitle(/Tymbox/);
  
  await page.goto('/en');
  await expect(page).toHaveTitle(/Tymbox/);
});
```

### Unit Test - Component
```javascript
// tests/unit/navigation.test.js
test('navigation renders correctly', () => {
  const { getByText } = render(<Navigation />);
  expect(getByText('Home')).toBeInTheDocument();
});
```

## When to Run Tests

- **Before deployment** - Run all tests
- **After changes** - Run relevant tests
- **Weekly** - Full test suite

## That's It!

Keep it simple. Test what breaks, not what works. Focus on user experience over code coverage.