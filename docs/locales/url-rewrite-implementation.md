# URL Rewrite Implementation for Multi-Locale Support

**Date**: July 16, 2025  
**Status**: ✅ Completed  
**Related**: `docs/architecture/02-seo-consistency-implementation.md`

## Overview

This document explains the URL rewrite system implementation for the single-domain multi-locale architecture, including the critical fixes that resolved 404 errors and established consistent URL mapping.

## Architecture Pattern

### Single-Domain with Locale Prefixes
- **Base URL**: `www.tymbox.cz`
- **Locale Structure**: `/{locale}/{seo-friendly-path}`
- **Supported Locales**: `cs` (Czech), `sk` (Slovak), `en` (English)

### URL Mapping Flow
```
SEO URL → Internal Page
/cs/online-dochazka → /cs/dochazka (page directory)
/sk/online-dochadzka → /sk/dochazka (same page, different locale)
/en/attendance → /en/dochazka (same page, English URL)
```

## Implementation Details

### 1. Centralized Configuration
**File**: `src/config/paths.js`

```javascript
export const pathMappings = {
  // Key MUST match the actual page directory name
  'dochazka': {
    cs: '/online-dochazka',    // Czech SEO URL
    sk: '/online-dochadzka',   // Slovak SEO URL  
    en: '/attendance'          // English SEO URL
  },
  'rezervace': {
    cs: '/online-rezervace',
    sk: '/online-rezervacie',
    en: '/bookings'
  },
  // ... all other pages
};
```

### 2. Automatic Rewrite Generation
```javascript
export const generateRewrites = () => {
  const rewrites = [];
  for (const [pageKey, locales] of Object.entries(pathMappings)) {
    for (const [locale, path] of Object.entries(locales)) {
      rewrites.push({
        source: `/${locale}${path}`,
        destination: `/${locale}/${pageKey}`,
      });
    }
  }
  return rewrites;
};
```

### 3. Next.js Integration
**File**: `next.config.js`

```javascript
const { generateRewrites } = require('./src/config/paths');

module.exports = withNextIntl({
  async rewrites() {
    return {
      beforeFiles: [
        ...generateRewrites(), // Automatically generated from config
      ],
      afterFiles: [],
      fallback: [],
    };
  }
});
```

## Page Directory Structure

### Required Pattern
Each page must have a directory structure that matches the page key:

```
src/app/[locale]/
├── dochazka/          ← Page key: 'dochazka'
│   └── page.tsx
├── rezervace/         ← Page key: 'rezervace'  
│   └── page.tsx
├── schuzky/           ← Page key: 'schuzky'
│   └── page.tsx
└── cenik/             ← Page key: 'cenik'
    └── page.tsx
```

### ⚠️ Critical Rule
**The page key in `pathMappings` MUST exactly match the directory name**

## Common Issues & Solutions

### Issue 1: 404 Errors on Valid URLs
**Symptom**: URL like `/cs/online-dochazka` returns 404
**Cause**: Page key mismatch
**Example**:
```javascript
// ❌ WRONG - causes 404
'attendance': {
  cs: '/online-dochazka'  // rewrites to /cs/attendance (doesn't exist)
}

// ✅ CORRECT
'dochazka': {
  cs: '/online-dochazka'  // rewrites to /cs/dochazka (exists)
}
```

### Issue 2: Inconsistent SEO URLs
**Symptom**: Same page has different URLs in sitemap vs navigation
**Cause**: Multiple path definitions
**Solution**: Use centralized `pathMappings` everywhere

### Issue 3: Missing Locale Variants
**Symptom**: Page works in one locale but not others
**Cause**: Missing locale definitions in `pathMappings`
**Solution**: Ensure all locales are defined for each page

## Debugging URL Issues

### 1. Check Rewrite Generation
```bash
node -e "
const { generateRewrites } = require('./src/config/paths');
console.log(generateRewrites().filter(r => r.source.includes('problem-url')));
"
```

### 2. Verify Directory Structure
```bash
ls -la src/app/[locale]/ | grep page-key
```

### 3. Test URL Resolution
```bash
curl -I http://localhost:3000/cs/your-url
```

## Locale-Specific Considerations

### Czech (cs)
- **URL Style**: Descriptive Czech phrases
- **Example**: `/online-dochazka`, `/planovac-schuzek`
- **Directory**: Uses Czech-based names for existing pages

### Slovak (sk)  
- **URL Style**: Slovak equivalent phrases
- **Example**: `/online-dochadzka`, `/planovac-stretnuti`
- **Directory**: Shares directory with Czech (same page key)

### English (en)
- **URL Style**: Clean English terms
- **Example**: `/attendance`, `/meetings`
- **Directory**: Uses same page key as Czech/Slovak

## SEO Benefits

### 1. Consistent URL Structure
- All URLs follow predictable patterns
- Cross-language linking works correctly
- Search engines can understand locale relationships

### 2. Proper Hreflang Implementation
```html
<link rel="alternate" hreflang="cs-CZ" href="https://www.tymbox.cz/cs/online-dochazka" />
<link rel="alternate" hreflang="sk-SK" href="https://www.tymbox.cz/sk/online-dochadzka" />
<link rel="alternate" hreflang="en-US" href="https://www.tymbox.cz/en/attendance" />
```

### 3. Sitemap Integration
- All locale variants automatically included
- Proper cross-references between languages
- No duplicate or missing URLs

## Adding New Pages

### Step-by-Step Process
1. **Create page directory**: `src/app/[locale]/new-page/page.tsx`
2. **Add to path mappings**:
   ```javascript
   'new-page': {
     cs: '/czech-seo-url',
     sk: '/slovak-seo-url', 
     en: '/english-seo-url'
   }
   ```
3. **Test all locales**:
   - `/cs/czech-seo-url`
   - `/sk/slovak-seo-url`
   - `/en/english-seo-url`
4. **Verify build**: `npm run build`

### Best Practices
- Use descriptive, SEO-friendly URLs for each locale
- Keep page keys neutral and directory-name compatible
- Test all locale variants before deployment
- Update sitemap automatically regenerates

## Migration Notes

### From Multi-Domain to Single-Domain
- Old domains redirect to new locale-prefixed URLs
- URL structure remains consistent within each locale
- SEO value preserved through proper redirects

### Legacy URL Support
```javascript
// Redirects handled in next.config.js
async redirects() {
  return [
    {
      source: '/:path*',
      has: [{ type: 'host', value: 'teambox.sk' }],
      destination: 'https://www.tymbox.cz/sk/:path*',
      permanent: true,
    }
  ];
}
```

This implementation provides a robust, maintainable URL structure that supports multiple locales while maintaining excellent SEO characteristics and user experience.