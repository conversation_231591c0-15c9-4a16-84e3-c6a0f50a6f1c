# ABOUTME: Simple startup-focused migration plan for single domain with locale prefixes
# ABOUTME: Fast MVP implementation for www.tymbox.cz/cs, /sk, /en structure

# Simple Migration Plan: Single Domain + Locale Prefixes

## Goal
Migrate to `www.tymbox.cz/cs`, `www.tymbox.cz/sk`, `www.tymbox.cz/en` structure.

## What Changes
- **Before**: tymbox.cz (Czech), teambox.sk (Slovak)
- **After**: www.tymbox.cz/cs (Czech), www.tymbox.cz/sk (Slovak), www.tymbox.cz/en (English)

## Simple 5-Step Plan

### 1. Update i18n Config (10 mins)
```javascript
// src/i18n/routing.js
export const routing = defineRouting({
  locales: ['cs', 'sk', 'en'],
  defaultLocale: 'cs',
  localePrefix: 'always' // All get /cs, /sk, /en
});
```

### 2. Move Pages to [locale] Structure (30 mins)
```
Before: src/app/page.tsx
After:  src/app/[locale]/page.tsx
```

### 3. Add Root Redirect (5 mins)
```javascript
// src/app/page.tsx
export default function RootPage() {
  redirect('/cs'); // Send everyone to Czech by default
}
```

### 4. Set Up Simple Redirects (20 mins)
```javascript
// next.config.js
async redirects() {
  return [
    // Old Slovak domain → new Slovak pages
    {
      source: '/:path*',
      has: [{ type: 'host', value: 'teambox.sk' }],
      destination: 'https://www.tymbox.cz/sk/:path*',
      permanent: true,
    },
    // Old Czech domain → new Czech pages  
    {
      source: '/:path*',
      has: [{ type: 'host', value: 'tymbox.cz' }],
      destination: 'https://www.tymbox.cz/cs/:path*',
      permanent: true,
    },
  ];
}
```

### 5. Update Sitemap (15 mins)
```javascript
// Generate URLs for all three locales
const locales = ['cs', 'sk', 'en'];
// Add /cs, /sk, /en prefix to all URLs
```

## Total Time: ~80 minutes

## What We're Skipping (Keep It Simple)
- ❌ Complex hreflang implementation
- ❌ Advanced SEO metadata
- ❌ Comprehensive testing suites
- ❌ Detailed monitoring setup
- ❌ Corporate-level documentation

## What We're Doing (MVP Focus)
- ✅ Basic locale structure working
- ✅ Simple redirects from old domains
- ✅ English locale available
- ✅ Basic sitemap with all locales
- ✅ Ship fast, improve later

## English Content (MVP)
1. Copy Czech messages to English
2. Translate homepage and key pages
3. Other pages show "English version coming soon"

## Testing (Simple)
- [ ] `/cs` pages work
- [ ] `/sk` pages work  
- [ ] `/en` pages work
- [ ] Old domains redirect properly
- [ ] Deploy

## Risks We Accept (MVP Trade-offs)
- Temporary SEO impact (will recover)
- Some broken external links (will fix gradually)
- Basic English content initially
- No complex analytics setup

---

*MVP motto: Make it work, make it right, make it fast*