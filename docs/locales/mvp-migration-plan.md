# ABOUTME: Simple MVP migration plan for adding English locale support
# ABOUTME: Focused on fast implementation for startup development needs

# MVP Locale Migration Plan

## Goal
Add English support at `tymbox.cz/en` while keeping existing Czech/Slovak setup working.

## Simple Changes Needed

### 1. Update i18n Config (5 mins)
```javascript
// src/i18n/routing.js
export const routing = defineRouting({
  locales: ['cs', 'sk', 'en'],
  defaultLocale: 'cs',
  localePrefix: 'as-needed' // Czech = no prefix, others = /sk, /en
});
```

### 2. Create English Messages (30 mins)
```bash
# Copy existing Czech messages
cp messages/cs.json messages/en.json
# Edit messages/en.json with English translations
```

### 3. Update App Structure (10 mins)
Move pages from `src/app/page.tsx` to `src/app/[locale]/page.tsx`

### 4. Update Sitemap (15 mins)
```javascript
// src/app/sitemap.ts - add English URLs
const locales = ['cs', 'sk', 'en'];
// Generate URLs for all three languages
```

### 5. Test and Deploy (30 mins)
- Test `/en` pages work
- Test existing Czech/Slovak still work
- Deploy

## Total Time: ~90 minutes

## What We're NOT Doing (Keep It Simple)
- ❌ Complex canonical URL strategy
- ❌ Advanced CDN configuration
- ❌ Corporate-level monitoring
- ❌ Complex analytics setup
- ❌ Extensive testing phases

## What We ARE Doing (MVP Focus)
- ✅ Basic English locale at `/en`
- ✅ Keep existing Czech/Slovak working
- ✅ Simple sitemap with all languages
- ✅ Basic hreflang tags
- ✅ Ship fast, iterate later

## Quick Implementation Checklist

- [ ] Update routing config
- [ ] Create English messages file
- [ ] Move pages to `[locale]` structure
- [ ] Update sitemap generator
- [ ] Test basic functionality
- [ ] Deploy to production

## English Content Strategy (MVP)
1. Start with homepage, pricing, contact
2. Use Czech fallback for other pages with "English coming soon" notice
3. Translate more pages over time

## Domain Handling (Keep Simple)
- Keep teambox.sk → tymbox.cz/sk redirect as-is
- Add English at tymbox.cz/en
- Done.

---

*Focus: Ship fast, perfect later*