# Current Application State and Setup

## Working State (as of commit)

All three local domains are currently working correctly:
- http://localhost:3000/ - De<PERSON>ult (Czech)
- http://local.teambox.sk:3000/ - Slovak version
- http://local.tymbox.cz:3000/ - Czech version

## Local Development Setup

### 1. Domain Configuration
The following entries are in `/etc/hosts`:
```
127.0.0.1 local.tymbox.cz
127.0.0.1 local.teambox.sk
```

### 2. Server Setup
- Development server runs on port 3000
- Started with: `npm run dev`
- Next.js 14.1.0

### 3. Domain Detection Flow
1. Request arrives at middleware
2. Hostname is extracted from headers
3. Domain is mapped to locale:
   - `local.tymbox.cz` → Czech (cs)
   - `local.teambox.sk` → Slovak (sk)
   - `localhost` → Czech (cs) - default
4. Locale is passed to next-intl for content rendering

### 4. Key Files for Domain Detection
- `/src/config/domains.js` - Domain configuration and mapping
- `/src/lib/domain-detector.js` - Domain detection utilities
- `/middleware.js` - Request handling and locale detection
- `/src/i18n/request.js` - Locale resolution for next-intl
- `/src/i18n/routing.js` - Routing configuration

### 5. Debug Logging
The following debug logs are available in the console:
- `[Domain Detection]` - Shows hostname parsing and matching
- `[Middleware]` - Shows request details and locale detection
- `[detectHostname]` - Shows which header/method was used

### 6. Known Issues to Fix
1. **Production Issue**: Slovak translation not working on teambox.sk (shows Czech)
2. **Potential Causes**:
   - Netlify header forwarding may differ from local setup
   - Domain detection might need Netlify-specific handling
   - Build process may need environment-specific configuration

### 7. Testing Locally
To test domain switching:
1. Start dev server: `npm run dev`
2. Access different domains:
   - Czech: http://local.tymbox.cz:3000/
   - Slovak: http://local.teambox.sk:3000/
3. Check console logs for domain detection
4. Verify content language matches domain

### 8. Expected Behavior
- Czech domain should show Czech content (Týmbox, docházka, etc.)
- Slovak domain should show Slovak content (Teambox, dochádzka, etc.)
- Registration links should redirect to appropriate language version
- SEO metadata should be domain-specific

## Next Steps
1. Set up MCP dev server tool for easier debugging
2. Test on Netlify preview to replicate production issue
3. Add Netlify-specific header detection if needed
4. Ensure domain detection works in all environments