{"permissions": {"allow": ["Bash(NEXT_PUBLIC_DEFAULT_LOCALE=sk npm run dev)", "Bash(NEXT_PUBLIC_DEFAULT_LOCALE=cs npm run dev)", "Bash(npm run build:sk:*)", "Bash(ls:*)", "Bash(npm run build:cz:*)", "Bash(find:*)", "mcp__puppeteer__puppeteer_evaluate", "mcp__puppeteer__puppeteer_screenshot", "Bash(npm run build:*)", "Bash(timeout 5 npm run dev:*)", "Bash(npm run dev:*)", "Bash(rm:*)", "Bash(./dev-server.sh:*)", "<PERSON><PERSON>(chmod:*)", "Bash(grep:*)", "Bash(node test-domains.js)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(./fix-locale-in-pages.sh:*)", "Bash(./fix-metadata-locale.sh:*)", "Bash(./fix-all-pages-comprehensive.sh:*)", "Bash(npm run lint)", "Bash(node:*)", "Bash(npx next lint:*)", "mcp__gemini__thinkdeep", "mcp__gemini__chat", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(cp:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(sed:*)", "mcp__gemini__analyze", "mcp__gemini__codereview", "Bash(npm install:*)", "Bash(npm run test:*)", "mcp__gemini__debug"], "deny": []}, "enableAllProjectMcpServers": false}