import Image from "next/image"
import Link from "next/link"
import { Star } from "lucide-react"

import { Button } from "@/components/ui/button"

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <header className="mx-auto max-w-7xl px-4 py-4">
        <div className="flex items-center justify-between rounded-full bg-white p-2 shadow-sm">
          <div className="flex items-center gap-8">
            <div className="flex items-center">
              <Image src="/logo.svg" alt="HEY Logo" width={100} height={40} className="h-10 w-auto" />
            </div>
            <nav className="hidden md:block">
              <ul className="flex space-x-6">
                <li>
                  <Link href="#" className="text-gray-700 hover:text-gray-900">
                    Features
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-gray-700 hover:text-gray-900">
                    Calendar
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-gray-700 hover:text-gray-900">
                    For domains
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-gray-700 hover:text-gray-900">
                    Pricing
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-gray-700 hover:text-gray-900">
                    FAQs
                  </Link>
                </li>
              </ul>
            </nav>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" className="rounded-full bg-gray-100 text-gray-800 hover:bg-gray-200">
              SIGN IN
            </Button>
            <Button className="rounded-full bg-navy-600 text-white hover:bg-navy-700">TRY HEY FREE</Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="mx-auto max-w-6xl px-4 py-12">
        <div className="rounded-3xl bg-white p-8 shadow-md">
          {/* Testimonials */}
          <div className="mb-8 grid grid-cols-1 gap-8 md:grid-cols-3">
            <div className="flex flex-col items-center text-center">
              <div className="mb-2 flex text-navy-500">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 fill-current" />
                ))}
              </div>
              <p className="italic text-gray-700">"Finally a privacy-respecting inbox"</p>
            </div>
            <div className="flex flex-col items-center text-center">
              <div className="mb-2 flex text-navy-500">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 fill-current" />
                ))}
              </div>
              <p className="italic text-gray-700">"Email has been re-invented"</p>
            </div>
            <div className="flex flex-col items-center text-center">
              <div className="mb-2 flex text-navy-500">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 fill-current" />
                ))}
              </div>
              <p className="italic text-gray-700">"I'm loving the HEY Calendar app"</p>
            </div>
          </div>

          {/* Headline */}
          <h1 className="mb-4 text-center text-5xl font-bold leading-tight md:text-6xl">
            <span className="text-navy-600">We finally fixed </span>
            <span className="text-slate-700">your</span>
            <br />
            <span className="text-slate-700">email </span>
            <span className="text-navy-600">+ </span>
            <span className="text-slate-700">calendar</span>
            <span className="text-navy-600">!</span>
          </h1>

          {/* Subheading */}
          <p className="mx-auto mb-10 max-w-4xl text-center text-xl font-bold text-gray-800 md:text-2xl">
            Gmail, Outlook, and Apple got complacent and took their eye off the ball. Then along came HEY.
          </p>

          {/* CTA Button */}
          <div className="mb-4 flex justify-center">
            <Button className="rounded-full bg-gradient-to-r from-navy-600 to-slate-600 px-8 py-6 text-lg font-semibold text-white hover:from-navy-700 hover:to-slate-700">
              TRY HEY FREE FOR 30-DAYS
            </Button>
          </div>
          <p className="text-center text-sm text-gray-500">No obligation, no CC required.</p>

          {/* App Preview */}
          <div className="mt-12 flex justify-center">
            <div className="relative w-full max-w-3xl overflow-hidden rounded-lg shadow-lg">
              <Image src="/app-preview.png" alt="HEY App Preview" width={1200} height={600} className="w-full" />
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
