#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

const LOG_FILE = path.join(__dirname, '..', 'server.log');
const PID_FILE = path.join(__dirname, '..', 'server.pid');

function startServer() {
  // Clear previous log
  fs.writeFileSync(LOG_FILE, '');
  
  // Start the dev server
  const server = spawn('npm', ['run', 'dev'], {
    cwd: path.join(__dirname, '..'),
    env: { ...process.env, FORCE_COLOR: '1' },
    detached: true,
    stdio: ['ignore', 'pipe', 'pipe']
  });

  // Write PID for later reference
  fs.writeFileSync(PID_FILE, server.pid.toString());

  // Create log streams
  const logStream = fs.createWriteStream(LOG_FILE, { flags: 'a' });

  // Pipe stdout and stderr to log file
  server.stdout.pipe(logStream);
  server.stderr.pipe(logStream);

  // Also output to console
  server.stdout.on('data', (data) => {
    process.stdout.write(data);
  });

  server.stderr.on('data', (data) => {
    process.stderr.write(data);
  });

  server.on('error', (err) => {
    console.error('Failed to start server:', err);
    process.exit(1);
  });

  console.log(`Server started with PID: ${server.pid}`);
  console.log(`Logs are being written to: ${LOG_FILE}`);
  
  // Unref to allow the parent process to exit
  server.unref();
}

function stopServer() {
  if (fs.existsSync(PID_FILE)) {
    const pid = parseInt(fs.readFileSync(PID_FILE, 'utf8'));
    try {
      process.kill(pid, 'SIGTERM');
      fs.unlinkSync(PID_FILE);
      console.log(`Server with PID ${pid} stopped`);
    } catch (err) {
      console.error(`Failed to stop server: ${err.message}`);
    }
  } else {
    console.log('No server PID file found');
  }
}

function readLogs(lines = 50) {
  if (fs.existsSync(LOG_FILE)) {
    const logs = fs.readFileSync(LOG_FILE, 'utf8');
    const logLines = logs.split('\n');
    const startIndex = Math.max(0, logLines.length - lines);
    console.log(logLines.slice(startIndex).join('\n'));
  } else {
    console.log('No log file found');
  }
}

function tailLogs() {
  if (!fs.existsSync(LOG_FILE)) {
    console.log('No log file found');
    return;
  }

  const tail = spawn('tail', ['-f', LOG_FILE]);
  
  tail.stdout.on('data', (data) => {
    process.stdout.write(data);
  });

  tail.stderr.on('data', (data) => {
    process.stderr.write(data);
  });

  tail.on('error', (err) => {
    console.error('Failed to tail logs:', err);
  });

  // Handle Ctrl+C
  process.on('SIGINT', () => {
    tail.kill();
    process.exit(0);
  });
}

// Parse command line arguments
const command = process.argv[2];

switch (command) {
  case 'start':
    startServer();
    break;
  case 'stop':
    stopServer();
    break;
  case 'logs':
    const lines = parseInt(process.argv[3]) || 50;
    readLogs(lines);
    break;
  case 'tail':
    tailLogs();
    break;
  default:
    console.log('Usage:');
    console.log('  node server-manager.js start    - Start the dev server');
    console.log('  node server-manager.js stop     - Stop the dev server');
    console.log('  node server-manager.js logs [n] - Show last n lines of logs (default: 50)');
    console.log('  node server-manager.js tail     - Follow log output');
}