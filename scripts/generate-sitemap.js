#!/usr/bin/env node

/**
 * This script can be used to manually generate a sitemap for all pages
 * Run with: node scripts/generate-sitemap.js
 */

const fs = require('fs');
const path = require('path');
const { routing } = require('../src/i18n/routing');

// Configure website URL and pages
const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://tymbox.cz';
const locales = routing.locales;
const defaultLocale = routing.defaultLocale;

// Define localized paths
const localizedPaths = {
  cs: {
    'attendance': '/online-dochazka',
    'bookings': '/online-rezervace',
    'meetings': '/planovac-schuzek',
    'pricing': '/tymbox-plus'
  },
  sk: {
    'attendance': '/online-dochadzka',
    'bookings': '/online-rezervacie',
    'meetings': '/planovac-stretnuti',
    'pricing': '/tymbox-plus'
  }
};

// Define all pages (using Czech as default)
const pages = [
  '', // Home page
  localizedPaths.cs.attendance,
  localizedPaths.cs.bookings,
  localizedPaths.cs.meetings,
  localizedPaths.cs.pricing,
  '/o-nas',
  '/kontakt',
  '/faq',
];

// Generate sitemap XML
const generateSitemap = () => {
  // XML header
  let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
  xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" ';
  xml += 'xmlns:xhtml="http://www.w3.org/1999/xhtml">\n';

  // For each page and locale, generate a URL entry
  for (const locale of locales) {
    for (const page of pages) {
      // Determine the path for this locale/page
      let path;
      
      // Get localized path for main features
      if (page === localizedPaths.cs.attendance) {
        path = `/${locale}${localizedPaths[locale].attendance}`;
      } else if (page === localizedPaths.cs.bookings) {
        path = `/${locale}${localizedPaths[locale].bookings}`;
      } else if (page === localizedPaths.cs.meetings) {
        path = `/${locale}${localizedPaths[locale].meetings}`;
      } else if (page === localizedPaths.cs.pricing) {
        path = `/${locale}${localizedPaths[locale].pricing}`;
      } else if (page === '') {
        path = locale === defaultLocale ? '/' : `/${locale}`;
      } else {
        path = `/${locale}${page}`;
      }
      
      const fullUrl = `${siteUrl}${path}`;
      const lastmod = new Date().toISOString();

      // Start URL entry
      xml += '  <url>\n';
      xml += `    <loc>${fullUrl}</loc>\n`;
      xml += `    <lastmod>${lastmod}</lastmod>\n`;
      xml += `    <changefreq>${page === '' ? 'daily' : 'weekly'}</changefreq>\n`;
      xml += `    <priority>${page === '' ? '1.0' : '0.8'}</priority>\n`;

      // Add alternate language versions (hreflang)
      for (const altLocale of locales) {
        // Determine the path for this alternate locale
        let altPath;
        
        // Get localized path for main features
        if (page === localizedPaths.cs.attendance) {
          altPath = `/${altLocale}${localizedPaths[altLocale].attendance}`;
        } else if (page === localizedPaths.cs.bookings) {
          altPath = `/${altLocale}${localizedPaths[altLocale].bookings}`;
        } else if (page === localizedPaths.cs.meetings) {
          altPath = `/${altLocale}${localizedPaths[altLocale].meetings}`;
        } else if (page === localizedPaths.cs.pricing) {
          altPath = `/${altLocale}${localizedPaths[altLocale].pricing}`;
        } else if (page === '') {
          altPath = altLocale === defaultLocale ? '/' : `/${altLocale}`;
        } else {
          altPath = `/${altLocale}${page}`;
        }
        
        const altUrl = `${siteUrl}${altPath}`;

        xml += `    <xhtml:link rel="alternate" hreflang="${altLocale}" href="${altUrl}" />\n`;
      }

      // Add x-default (points to default locale)
      let xDefaultPath;
      
      // Get localized path for main features
      if (page === localizedPaths.cs.attendance) {
        xDefaultPath = `/${defaultLocale}${localizedPaths[defaultLocale].attendance}`;
      } else if (page === localizedPaths.cs.bookings) {
        xDefaultPath = `/${defaultLocale}${localizedPaths[defaultLocale].bookings}`;
      } else if (page === localizedPaths.cs.meetings) {
        xDefaultPath = `/${defaultLocale}${localizedPaths[defaultLocale].meetings}`;
      } else if (page === localizedPaths.cs.pricing) {
        xDefaultPath = `/${defaultLocale}${localizedPaths[defaultLocale].pricing}`;
      } else if (page === '') {
        xDefaultPath = '/';
      } else {
        xDefaultPath = `/${defaultLocale}${page}`;
      }
      
      const xDefaultUrl = `${siteUrl}${xDefaultPath}`;
      xml += `    <xhtml:link rel="alternate" hreflang="x-default" href="${xDefaultUrl}" />\n`;

      // End URL entry
      xml += '  </url>\n';
    }
  }

  // XML footer
  xml += '</urlset>';

  return xml;
};

// Generate the sitemap
const sitemap = generateSitemap();

// Ensure public directory exists
const publicDir = path.join(__dirname, '..', 'public');
if (!fs.existsSync(publicDir)) {
  fs.mkdirSync(publicDir, { recursive: true });
}

// Write the sitemap to a file
fs.writeFileSync(path.join(publicDir, 'manual-sitemap.xml'), sitemap);

console.log('Sitemap generated at public/manual-sitemap.xml');

// Generate robots.txt with reference to the sitemap
const robotsTxt = `
User-agent: *
Allow: /

Sitemap: ${siteUrl}/sitemap.xml
Sitemap: ${siteUrl}/manual-sitemap.xml
`;

fs.writeFileSync(path.join(publicDir, 'robots.txt'), robotsTxt);
console.log('robots.txt generated at public/robots.txt');