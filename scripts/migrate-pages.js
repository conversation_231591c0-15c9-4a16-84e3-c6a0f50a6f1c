#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to recursively find all page.tsx files
function findPageFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      // Skip node_modules and .next directories
      if (!item.startsWith('.') && item !== 'node_modules') {
        findPageFiles(fullPath, files);
      }
    } else if (item === 'page.tsx' || item === 'layout.tsx') {
      files.push(fullPath);
    }
  }
  
  return files;
}

// Function to update a page file
function updatePageFile(filePath) {
  console.log(`Processing: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // Check if file already uses the new system
  if (content.includes('generatePageMetadata') || content.includes('generateHreflangMetadata')) {
    console.log(`  ✓ Already updated`);
    return;
  }
  
  // Check if file has generateMetadata function
  if (content.includes('generateMetadata')) {
    console.log(`  → Updating generateMetadata function`);
    
    // Replace import if it exists
    if (content.includes("import { generatePageMetadata } from '@/lib/metadata';")) {
      // Already has correct import
    } else if (content.includes("from '@/lib/metadata'")) {
      content = content.replace(
        /import\s*{[^}]*}\s*from\s*'@\/lib\/metadata';/,
        "import { generatePageMetadata } from '@/lib/metadata';"
      );
      modified = true;
    } else {
      // Add import at the top of the file (after other imports)
      const importMatch = content.match(/(import[^;]+;[\s\n]*)+/);
      if (importMatch) {
        const lastImportEnd = importMatch.index + importMatch[0].length;
        content = content.slice(0, lastImportEnd) + 
                 "\nimport { generatePageMetadata } from '@/lib/metadata';\n" + 
                 content.slice(lastImportEnd);
        modified = true;
      }
    }
    
    // Update generateMetadata function calls
    content = content.replace(
      /return\s+generatePageMetadata\(/g,
      'return generatePageMetadata('
    );
    
    modified = true;
  } else {
    console.log(`  → No generateMetadata function found, skipping`);
  }
  
  if (modified) {
    fs.writeFileSync(filePath, content);
    console.log(`  ✓ Updated successfully`);
  }
}

// Main execution
console.log('🔄 Migrating pages to new metadata system...\n');

const appDir = path.join(process.cwd(), 'src', 'app');
const pageFiles = findPageFiles(appDir);

console.log(`Found ${pageFiles.length} page files:\n`);

pageFiles.forEach(updatePageFile);

console.log('\n✅ Migration completed!');
console.log('\n📝 Next steps:');
console.log('1. Review the updated files');
console.log('2. Test both Czech and Slovak builds:');
console.log('   npm run build:cz');
console.log('   npm run build:sk');
console.log('3. Check that hreflang tags are working correctly');
console.log('4. Deploy to your Netlify sites'); 