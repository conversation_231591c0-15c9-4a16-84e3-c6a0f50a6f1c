{"name": "tymbox2-landing", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:prod": "next build && next-sitemap", "start": "next start", "lint": "next lint", "postbuild": "next-sitemap", "test": "vitest", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:domains": "echo 'Testing domain detection locally - add domains to /etc/hosts'"}, "dependencies": {"gray-matter": "^4.0.3", "lucide-react": "^0.360.0", "next": "^14.2.30", "next-intl": "^3.9.0", "next-sitemap": "^4.2.3", "react": "^18.2.0", "react-dom": "^18.2.0", "remark": "^15.0.1", "remark-gfm": "^4.0.1", "remark-html": "^16.0.1", "resend": "^4.5.1", "zod": "^3.22.4"}, "devDependencies": {"@playwright/test": "^1.40.0", "@tailwindcss/line-clamp": "^0.4.4", "@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^14.0.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.18", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "jsdom": "^23.0.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5", "vitest": "^3.2.4"}}