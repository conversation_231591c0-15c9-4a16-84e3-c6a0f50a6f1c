/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/app/**/*.{js,ts,jsx,tsx}',
    './src/components/**/*.{js,ts,jsx,tsx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['var(--font-inter)', 'sans-serif'],
      },
      colors: {
        'green-primary': 'var(--green-primary)',
        'green-secondary': 'var(--green-secondary)',
        'green-light': 'var(--green-light)',
        'charcoal': 'var(--charcoal)',
        'stone': 'var(--stone)',
        'bg-light': 'var(--bg-light)',
        'bg-alt': 'var(--bg-alt)',
        'accent': 'var(--accent)',
        'yellow': 'var(--yellow)',
        'blue': 'var(--blue)',
        'purple': 'var(--purple)',
      },
      borderRadius: {
        'sm': '4px',
        'md': '8px',
        'lg': '12px',
        'xl': '24px',
      },
    },
  },
  plugins: [],
} 