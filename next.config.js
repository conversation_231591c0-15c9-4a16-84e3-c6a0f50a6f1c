const withNextIntl = require('next-intl/plugin')(
  './src/i18n/request.js'
);
const { generateRewrites } = require('./src/config/paths');

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  images: {
    domains: [],
  },
  // output: 'standalone', // Disabled for now
  
  // Remove hardcoded env vars - domain detection happens at runtime
  // env: {},
  
  experimental: {
    optimizePackageImports: ['lucide-react'],
  },
  
  webpack: (config, { dev, isServer }) => {
    if (!dev && !isServer) {
      config.optimization.usedExports = true;
      config.optimization.sideEffects = false;
      
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
        },
      };
    }
    
    return config;
  },
  
  async headers() {
    return [
      {
        source: '/sitemap.xml',
        headers: [
          {
            key: 'Content-Type',
            value: 'application/xml',
          },
        ],
      },
      {
        source: '/sitemap-:slug*.xml',
        headers: [
          {
            key: 'Content-Type',
            value: 'application/xml',
          },
        ],
      },
    ];
  },
  
  async rewrites() {
    return {
      beforeFiles: [
        // SEO-friendly URL rewrites generated from centralized config
        ...generateRewrites(),
      ],
      afterFiles: [],
      fallback: [],
    };
  },
  
  async redirects() {
    return [
      // Old Slovak domain → new Slovak pages
      {
        source: '/:path*',
        has: [{ type: 'host', value: 'teambox.sk' }],
        destination: 'https://www.tymbox.cz/sk/:path*',
        permanent: true,
      },
      {
        source: '/:path*',
        has: [{ type: 'host', value: 'www.teambox.sk' }],
        destination: 'https://www.tymbox.cz/sk/:path*',
        permanent: true,
      },
      // Old Czech domain → new Czech pages  
      {
        source: '/:path*',
        has: [{ type: 'host', value: 'tymbox.cz' }],
        destination: 'https://www.tymbox.cz/cs/:path*',
        permanent: true,
      },
    ];
  },
  
  trailingSlash: false,
};

module.exports = withNextIntl(nextConfig); 