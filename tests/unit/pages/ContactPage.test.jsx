// ABOUTME: Unit tests for Contact page component rendering and metadata generation
// ABOUTME: Tests page structure, translation integration, and form inclusion

import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'

// Mock ContactForm component
vi.mock('@/components/ContactForm', () => ({
  default: () => <div data-testid="contact-form">Mocked Contact Form</div>
}))

// Mock next-intl
const mockTranslations = {
  ContactPage: {
    mainHeading: 'Kontaktujte nás',
    subtitle: 'Máte otázku nebo zpětnou vazbu? Rádi si s vámi popovídáme.',
    metaTitle: 'Kontakt - Týmbox | Napište nám',
    metaDescription: 'Kontaktujte tým Týmbox. Máte otázku nebo zpětnou vazbu? Rádi vám pomůžeme.'
  }
}

vi.mock('next-intl', () => ({
  useTranslations: vi.fn((namespace) => (key) => {
    return mockTranslations[namespace]?.[key] || key
  })
}))

vi.mock('next-intl/server', () => ({
  setRequestLocale: vi.fn(),
  getTranslations: vi.fn(async (namespace) => (key) => {
    return mockTranslations[namespace]?.[key] || key
  })
}))

import ContactPage, { generateMetadata } from '@/app/[locale]/kontakt/page'

describe('ContactPage', () => {
  const mockParams = { locale: 'cs' }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Component Rendering', () => {
    it('renders contact page correctly', () => {
      render(<ContactPage params={mockParams} />)

      // Check for main heading
      expect(screen.getByText('Kontaktujte nás')).toBeInTheDocument()
      
      // Check for subtitle
      expect(screen.getByText('Máte otázku nebo zpětnou vazbu? Rádi si s vámi popovídáme.')).toBeInTheDocument()
      
      // Check that ContactForm is included
      expect(screen.getByTestId('contact-form')).toBeInTheDocument()
    })

    it('sets the correct locale', async () => {
      const { setRequestLocale } = await import('next-intl/server')
      render(<ContactPage params={mockParams} />)
      
      expect(setRequestLocale).toHaveBeenCalledWith('cs')
    })

    it('uses correct translation namespace', async () => {
      const { useTranslations } = await import('next-intl')
      render(<ContactPage params={mockParams} />)
      
      expect(useTranslations).toHaveBeenCalledWith('ContactPage')
    })
  })

  describe('Page Structure', () => {
    it('has correct page layout classes', () => {
      const { container } = render(<ContactPage params={mockParams} />)
      
      const mainDiv = container.firstChild
      expect(mainDiv).toHaveClass('min-h-screen', 'bg-gray-50', 'pt-24', 'md:pt-32')
    })

    it('renders main heading with correct styling', () => {
      render(<ContactPage params={mockParams} />)
      
      const heading = screen.getByRole('heading', { level: 1 })
      expect(heading).toHaveClass('mb-4', 'text-4xl', 'font-black', 'tracking-tight', 'text-charcoal', 'md:text-5xl')
      expect(heading).toHaveTextContent('Kontaktujte nás')
    })

    it('renders subtitle with correct styling', () => {
      render(<ContactPage params={mockParams} />)
      
      const subtitle = screen.getByText('Máte otázku nebo zpětnou vazbu? Rádi si s vámi popovídáme.')
      expect(subtitle.tagName).toBe('P')
      expect(subtitle).toHaveClass('mx-auto', 'max-w-2xl', 'text-lg', 'text-gray-700', 'md:text-xl')
    })

    it('has responsive container layout', () => {
      const { container } = render(<ContactPage params={mockParams} />)
      
      const section = container.querySelector('section')
      expect(section).toHaveClass('container', 'mx-auto', 'px-4')
      
      const contentDiv = container.querySelector('.max-w-3xl')
      expect(contentDiv).toHaveClass('max-w-3xl', 'mx-auto', 'text-center', 'mb-12', 'md:mb-16')
    })
  })

  describe('Accessibility', () => {
    it('has proper heading hierarchy', () => {
      render(<ContactPage params={mockParams} />)
      
      const mainHeading = screen.getByRole('heading', { level: 1 })
      expect(mainHeading).toBeInTheDocument()
      expect(mainHeading).toHaveTextContent('Kontaktujte nás')
    })

    it('has semantic HTML structure', () => {
      const { container } = render(<ContactPage params={mockParams} />)
      
      const section = container.querySelector('section')
      expect(section).toBeInTheDocument()
      
      const heading = screen.getByRole('heading', { level: 1 })
      expect(heading).toBeInTheDocument()
    })
  })

  describe('Translation Integration', () => {
    it('renders translated content correctly', () => {
      render(<ContactPage params={mockParams} />)
      
      expect(screen.getByText('Kontaktujte nás')).toBeInTheDocument()
      expect(screen.getByText('Máte otázku nebo zpětnou vazbu? Rádi si s vámi popovídáme.')).toBeInTheDocument()
    })

    it('handles translation keys that return the key itself', () => {
      mockUseTranslations.mockImplementationOnce((namespace) => (key) => key)
      
      render(<ContactPage params={mockParams} />)
      
      expect(screen.getByText('mainHeading')).toBeInTheDocument()
      expect(screen.getByText('subtitle')).toBeInTheDocument()
    })
  })

  describe('Component Integration', () => {
    it('includes ContactForm component', () => {
      render(<ContactPage params={mockParams} />)
      
      expect(screen.getByTestId('contact-form')).toBeInTheDocument()
      expect(screen.getByText('Mocked Contact Form')).toBeInTheDocument()
    })
  })

  describe('Locale Parameter Handling', () => {
    it('handles different locale parameters', async () => {
      const { setRequestLocale } = await import('next-intl/server')
      const slovakParams = { locale: 'sk' }
      
      render(<ContactPage params={slovakParams} />)
      
      expect(setRequestLocale).toHaveBeenCalledWith('sk')
    })

    it('handles English locale parameter', async () => {
      const { setRequestLocale } = await import('next-intl/server')
      const englishParams = { locale: 'en' }
      
      render(<ContactPage params={englishParams} />)
      
      expect(setRequestLocale).toHaveBeenCalledWith('en')
    })
  })
})

describe('ContactPage generateMetadata', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Metadata Generation', () => {
    it('generates correct metadata with translations', async () => {
      const metadata = await generateMetadata()

      expect(metadata.title).toBe('Kontakt - Týmbox | Napište nám')
      expect(metadata.description).toBe('Kontaktujte tým Týmbox. Máte otázku nebo zpětnou vazbu? Rádi vám pomůžeme.')
    })

    it('calls getTranslations with correct namespace', async () => {
      const { getTranslations } = await import('next-intl/server')
      await generateMetadata()

      expect(getTranslations).toHaveBeenCalledWith('ContactPage')
    })

    it('generates language alternates correctly', async () => {
      const metadata = await generateMetadata()

      expect(metadata.alternates.languages).toBeDefined()
      expect(metadata.alternates.languages['en']).toBe('/en/kontakt')
      expect(metadata.alternates.languages['sk']).toBe('/sk/kontakt')
      expect(metadata.alternates.languages['cs']).toBe('/cs/kontakt')
      expect(metadata.alternates.languages['x-default']).toBe('/en/kontakt')
    })

    it('uses fallback metadata when translations fail', async () => {
      const { getTranslations } = await import('next-intl/server')
      getTranslations.mockRejectedValueOnce(new Error('Translation error'))
      
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      const metadata = await generateMetadata()

      expect(metadata.title).toBe('Contact Us - Týmbox')
      expect(metadata.description).toBe('Get in touch with Tymbox. Send us your questions or feedback.')
      expect(consoleSpy).toHaveBeenCalledWith(
        'Missing metadata translations for ContactPage:',
        expect.any(Error)
      )

      consoleSpy.mockRestore()
    })

    it('handles translation errors gracefully', async () => {
      const { getTranslations } = await import('next-intl/server')
      getTranslations.mockImplementationOnce(async (namespace) => (key) => {
        if (key === 'metaTitle') throw new Error('Translation missing')
        return mockTranslations[namespace]?.[key] || key
      })

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      const metadata = await generateMetadata()

      // Should still return some metadata even if translation fails
      expect(metadata.title).toBeDefined()
      expect(metadata.description).toBeDefined()
      expect(metadata.alternates).toBeDefined()

      consoleSpy.mockRestore()
    })

    it('has consistent URL structure in alternates', async () => {
      const metadata = await generateMetadata()

      const { languages } = metadata.alternates

      // All URLs should follow the same pattern
      Object.values(languages).forEach(url => {
        expect(url).toMatch(/^\/[a-z]{2}\/kontakt$/)
      })

      // x-default should point to English
      expect(languages['x-default']).toBe('/en/kontakt')
    })
  })

  describe('SEO Best Practices', () => {
    it('provides meaningful title and description', async () => {
      const metadata = await generateMetadata()

      expect(metadata.title).toContain('Kontakt')
      expect(metadata.title).toContain('Týmbox')
      expect(metadata.description).toContain('Kontaktujte')
      expect(metadata.description.length).toBeGreaterThan(50)
      expect(metadata.description.length).toBeLessThan(160)
    })

    it('includes all required metadata fields', async () => {
      const metadata = await generateMetadata()

      expect(metadata.title).toBeDefined()
      expect(metadata.description).toBeDefined()
      expect(metadata.alternates).toBeDefined()
      expect(metadata.alternates.languages).toBeDefined()
    })
  })
})