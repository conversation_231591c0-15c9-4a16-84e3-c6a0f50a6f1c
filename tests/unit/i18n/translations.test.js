// ABOUTME: Unit tests for internationalization functionality and translation completeness
// ABOUTME: Validates that all required translation keys exist across locales and routing works correctly

import { describe, it, expect } from 'vitest'
import { routing } from '@/i18n/routing'

// Import translation files
import csTranslations from '../../../messages/cs.json'
import skTranslations from '../../../messages/sk.json'
import enTranslations from '../../../messages/en.json'

describe('I18n Configuration', () => {
  describe('Routing Configuration', () => {
    it('has correct locales configured', () => {
      expect(routing.locales).toEqual(['cs', 'sk', 'en'])
    })

    it('has correct default locale', () => {
      expect(routing.defaultLocale).toBe('cs')
    })

    it('has locale prefix set to always', () => {
      expect(routing.localePrefix).toBe('always')
    })
  })
})

describe('Translation Files', () => {
  const allTranslations = {
    cs: csTranslations,
    sk: skTranslations,
    en: enTranslations
  }

  describe('File Structure', () => {
    it('loads all translation files successfully', () => {
      expect(csTranslations).toBeDefined()
      expect(skTranslations).toBeDefined()
      expect(enTranslations).toBeDefined()
    })

    it('has common namespace in all locales', () => {
      Object.keys(allTranslations).forEach(locale => {
        expect(allTranslations[locale].common).toBeDefined()
      })
    })

    it('has header namespace in all locales', () => {
      Object.keys(allTranslations).forEach(locale => {
        expect(allTranslations[locale].header).toBeDefined()
      })
    })

    it('has paths namespace in all locales', () => {
      Object.keys(allTranslations).forEach(locale => {
        expect(allTranslations[locale].paths).toBeDefined()
      })
    })

    it('has footer namespace in all locales', () => {
      Object.keys(allTranslations).forEach(locale => {
        expect(allTranslations[locale].footer).toBeDefined()
      })
    })
  })

  describe('Translation Key Completeness', () => {
    // Helper function to get all keys from an object recursively
    const getAllKeys = (obj, prefix = '') => {
      let keys = []
      for (const key in obj) {
        const fullKey = prefix ? `${prefix}.${key}` : key
        if (typeof obj[key] === 'object' && obj[key] !== null) {
          keys = keys.concat(getAllKeys(obj[key], fullKey))
        } else {
          keys.push(fullKey)
        }
      }
      return keys
    }

    it('has consistent keys across all locales', () => {
      const csKeys = getAllKeys(csTranslations)
      const skKeys = getAllKeys(skTranslations)
      const enKeys = getAllKeys(enTranslations)

      // Check that all locales have the same keys
      expect(skKeys.sort()).toEqual(csKeys.sort())
      expect(enKeys.sort()).toEqual(csKeys.sort())
    })

    it('has all required header navigation keys', () => {
      const requiredHeaderKeys = [
        'header.attendance',
        'header.bookings', 
        'header.meetings',
        'header.pricing',
        'header.login',
        'header.menu'
      ]

      Object.keys(allTranslations).forEach(locale => {
        requiredHeaderKeys.forEach(key => {
          const [namespace, subkey] = key.split('.')
          expect(allTranslations[locale][namespace][subkey]).toBeDefined()
          expect(allTranslations[locale][namespace][subkey]).not.toBe('')
        })
      })
    })

    it('has all required path keys', () => {
      const requiredPathKeys = [
        'paths.attendance',
        'paths.bookings',
        'paths.meetings', 
        'paths.pricing',
        'paths.contact'
      ]

      Object.keys(allTranslations).forEach(locale => {
        requiredPathKeys.forEach(key => {
          const [namespace, subkey] = key.split('.')
          expect(allTranslations[locale][namespace][subkey]).toBeDefined()
          expect(allTranslations[locale][namespace][subkey]).not.toBe('')
        })
      })
    })

    it('has all required common keys', () => {
      const requiredCommonKeys = [
        'common.title',
        'common.metaTitle',
        'common.metaDescription',
        'common.getStarted'
      ]

      Object.keys(allTranslations).forEach(locale => {
        requiredCommonKeys.forEach(key => {
          const [namespace, subkey] = key.split('.')
          expect(allTranslations[locale][namespace][subkey]).toBeDefined()
          expect(allTranslations[locale][namespace][subkey]).not.toBe('')
        })
      })
    })
  })

  describe('Translation Content Quality', () => {
    it('has no empty translation values', () => {
      const checkForEmptyValues = (obj, path = '') => {
        for (const key in obj) {
          const currentPath = path ? `${path}.${key}` : key
          if (typeof obj[key] === 'object' && obj[key] !== null) {
            checkForEmptyValues(obj[key], currentPath)
          } else {
            expect(obj[key]).not.toBe('')
            expect(obj[key]).not.toBe(null)
            expect(obj[key]).not.toBe(undefined)
          }
        }
      }

      Object.keys(allTranslations).forEach(locale => {
        checkForEmptyValues(allTranslations[locale])
      })
    })

    it('has properly formatted URLs in paths', () => {
      Object.keys(allTranslations).forEach(locale => {
        const paths = allTranslations[locale].paths
        for (const pathKey in paths) {
          const path = paths[pathKey]
          // All paths should start with /
          expect(path).toMatch(/^\//)
          // Paths should not end with / (except root)
          if (path !== '/') {
            expect(path).not.toMatch(/\/$/)
          }
          // Paths should not contain spaces
          expect(path).not.toMatch(/\s/)
        }
      })
    })

    it('has consistent CTA button texts', () => {
      Object.keys(allTranslations).forEach(locale => {
        const common = allTranslations[locale].common
        // Check that all getStarted variations exist
        expect(common.getStarted).toBeDefined()
        expect(common.getStarted1).toBeDefined()
        expect(common.getStarted2).toBeDefined()
        expect(common.getStarted3).toBeDefined()
        expect(common.getStarted4).toBeDefined()
      })
    })
  })

  describe('Locale-Specific Content', () => {
    it('has different content for different locales', () => {
      // Check that translations are actually different between locales
      expect(csTranslations.common.title).not.toBe(skTranslations.common.title)
      expect(csTranslations.common.title).not.toBe(enTranslations.common.title)
      expect(skTranslations.common.title).not.toBe(enTranslations.common.title)
    })

    it('has language-appropriate navigation URLs', () => {
      // Czech paths should contain Czech SEO-friendly URLs
      expect(csTranslations.paths.attendance).toContain('dochazka')
      expect(csTranslations.paths.bookings).toContain('rezervace')
      
      // Slovak paths should be different from Czech
      expect(skTranslations.paths.attendance).not.toBe(csTranslations.paths.attendance)
      expect(skTranslations.paths.bookings).not.toBe(csTranslations.paths.bookings)
    })

    it('has proper meta titles for SEO', () => {
      // Each locale should have unique meta titles
      expect(csTranslations.common.metaTitle).toContain('Týmbox')
      expect(skTranslations.common.metaTitle).toContain('Týmbox')
      expect(enTranslations.common.metaTitle).toContain('Týmbox')
      
      // But they should be in different languages
      expect(csTranslations.common.metaTitle).not.toBe(skTranslations.common.metaTitle)
      expect(csTranslations.common.metaTitle).not.toBe(enTranslations.common.metaTitle)
    })
  })

  describe('Contact Form Translations', () => {
    it('has ContactForm namespace in all locales (if exists)', () => {
      Object.keys(allTranslations).forEach(locale => {
        const translations = allTranslations[locale]
        if (translations.ContactForm) {
          expect(translations.ContactForm.labelName).toBeDefined()
          expect(translations.ContactForm.labelEmail).toBeDefined()
          expect(translations.ContactForm.labelMessage).toBeDefined()
          expect(translations.ContactForm.buttonSend).toBeDefined()
          expect(translations.ContactForm.buttonSending).toBeDefined()
          expect(translations.ContactForm.successMessage).toBeDefined()
          expect(translations.ContactForm.errorMessage).toBeDefined()
          expect(translations.ContactForm.errorAllFieldsRequired).toBeDefined()
        }
      })
    })
  })

  describe('Navigation Path Consistency', () => {
    it('ensures all header navigation items have corresponding paths', () => {
      Object.keys(allTranslations).forEach(locale => {
        const header = allTranslations[locale].header
        const paths = allTranslations[locale].paths
        
        // Check that each header item has a corresponding path
        if (header.attendance && paths.attendance) {
          expect(paths.attendance).toBeDefined()
        }
        if (header.bookings && paths.bookings) {
          expect(paths.bookings).toBeDefined()
        }
        if (header.meetings && paths.meetings) {
          expect(paths.meetings).toBeDefined()
        }
        if (header.pricing && paths.pricing) {
          expect(paths.pricing).toBeDefined()
        }
      })
    })
  })

  describe('URL Structure Validation', () => {
    it('validates that paths follow SEO-friendly URL patterns', () => {
      Object.keys(allTranslations).forEach(locale => {
        const paths = allTranslations[locale].paths
        for (const pathKey in paths) {
          const path = paths[pathKey]
          // Should be lowercase
          expect(path).toBe(path.toLowerCase())
          // Should use hyphens instead of spaces
          expect(path).not.toMatch(/\s/)
          // Should not have special characters except hyphens and slashes
          expect(path).toMatch(/^\/[a-z0-9\-\/]*$/)
        }
      })
    })
  })
})