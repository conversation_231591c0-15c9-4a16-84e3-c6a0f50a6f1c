// ABOUTME: Comprehensive unit tests for metadata generation utilities
// ABOUTME: Tests SEO metadata, canonical URLs, hreflang generation, and multi-locale support

import { describe, it, expect, vi, beforeEach } from 'vitest'

// Mock config modules
vi.mock('@/config/site', () => ({
  siteConfig: {
    baseUrl: 'https://test.tymbox.cz',
    name: 'Týmbox',
    defaultLocale: 'cs',
    locales: ['cs', 'sk', 'en']
  }
}))

vi.mock('@/config/domains', () => ({
  BASE_URL: 'https://test.tymbox.cz',
  getLocaleConfig: vi.fn((locale) => {
    const configs = {
      cs: { locale: 'cs', language: 'cs-CZ', name: 'Týmbox CZ' },
      sk: { locale: 'sk', language: 'sk-SK', name: 'Teambox SK' },
      en: { locale: 'en', language: 'en-US', name: 'Tymbox EN' }
    }
    return configs[locale] || configs.cs
  }),
  getAllLocales: vi.fn(() => ['cs', 'sk', 'en'])
}))

// Mock next-intl
const mockTranslations = {
  cs: {
    common: {
      metaTitle: 'Týmbox Test Title CS',
      metaDescription: 'Týmbox Test Description CS',
      title: 'Docházka CS'
    }
  },
  sk: {
    common: {
      metaTitle: 'Teambox Test Title SK',
      metaDescription: 'Teambox Test Description SK',
      title: 'Dochádzka SK'
    }
  },
  en: {
    common: {
      metaTitle: 'Tymbox Test Title EN',
      metaDescription: 'Tymbox Test Description EN',
      title: 'Attendance EN'
    }
  }
}

vi.mock('next-intl/server', () => ({
  getTranslations: vi.fn(async ({ locale, namespace }) => {
    const translations = mockTranslations[locale]?.[namespace] || {}
    return (key) => translations[key] || undefined
  })
}))

import { generatePageMetadata, generateHreflangMetadata } from '@/lib/metadata'

describe('Metadata Utility Functions', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('generatePageMetadata', () => {
    describe('Basic Functionality', () => {
      it('generates metadata with default values', async () => {
        const metadata = await generatePageMetadata({
          locale: 'cs',
          pagePath: '/',
          namespace: 'common'
        })

        expect(metadata).toBeDefined()
        expect(metadata.title).toContain('Týmbox Test Title CS')
        expect(metadata.description).toBe('Týmbox Test Description CS')
        expect(metadata.metadataBase).toEqual(new URL('https://test.tymbox.cz'))
      })

      it('handles missing locale parameter', async () => {
        const metadata = await generatePageMetadata({
          pagePath: '/',
          namespace: 'common'
        })

        expect(metadata).toBeDefined()
        expect(metadata.title).toContain('Týmbox')
      })

      it('normalizes page paths correctly', async () => {
        const metadata = await generatePageMetadata({
          locale: 'cs',
          pagePath: 'test-page',
          namespace: 'common'
        })

        expect(metadata.alternates.canonical).toBe('https://test.tymbox.cz/cs/test-page')
      })
    })

    describe('Title Generation', () => {
      it('uses metaTitle from translations when available', async () => {
        const metadata = await generatePageMetadata({
          locale: 'cs',
          pagePath: '/',
          namespace: 'common'
        })

        expect(metadata.title).toContain('Týmbox Test Title CS')
      })

      it('falls back to title from translations when metaTitle is missing', async () => {
        mockGetTranslations.mockImplementationOnce(async ({ locale, namespace }) => {
          return (key) => {
            if (key === 'title') return 'Fallback Title'
            return undefined
          }
        })

        const metadata = await generatePageMetadata({
          locale: 'cs',
          pagePath: '/',
          namespace: 'common'
        })

        expect(metadata.title).toContain('Fallback Title')
      })

      it('uses default title when translations fail', async () => {
        mockGetTranslations.mockRejectedValueOnce(new Error('Translation error'))

        const metadata = await generatePageMetadata({
          locale: 'cs',
          pagePath: '/',
          namespace: 'common',
          defaultTitle: 'Custom Default Title'
        })

        expect(metadata.title).toContain('Custom Default Title')
      })

      it('appends site name to title if not already present', async () => {
        mockGetTranslations.mockImplementationOnce(async ({ locale, namespace }) => {
          return (key) => {
            if (key === 'metaTitle') return 'Page Title Without Site Name'
            return undefined
          }
        })

        const metadata = await generatePageMetadata({
          locale: 'cs',
          pagePath: '/',
          namespace: 'common'
        })

        expect(metadata.title).toBe('Page Title Without Site Name | Týmbox')
      })

      it('does not duplicate site name in title', async () => {
        mockGetTranslations.mockImplementationOnce(async ({ locale, namespace }) => {
          return (key) => {
            if (key === 'metaTitle') return 'Page Title | Týmbox'
            return undefined
          }
        })

        const metadata = await generatePageMetadata({
          locale: 'cs',
          pagePath: '/',
          namespace: 'common'
        })

        expect(metadata.title).toBe('Page Title | Týmbox')
      })
    })

    describe('Description Generation', () => {
      it('uses metaDescription from translations', async () => {
        const metadata = await generatePageMetadata({
          locale: 'sk',
          pagePath: '/',
          namespace: 'common'
        })

        expect(metadata.description).toBe('Teambox Test Description SK')
      })

      it('falls back to default description when translation is missing', async () => {
        mockGetTranslations.mockImplementationOnce(async ({ locale, namespace }) => {
          return (key) => undefined
        })

        const metadata = await generatePageMetadata({
          locale: 'cs',
          pagePath: '/',
          namespace: 'common',
          defaultDescription: 'Custom Default Description'
        })

        expect(metadata.description).toBe('Custom Default Description')
      })
    })

    describe('Canonical URL Generation', () => {
      it('generates correct canonical URL for root page', async () => {
        const metadata = await generatePageMetadata({
          locale: 'cs',
          pagePath: '/',
          namespace: 'common'
        })

        expect(metadata.alternates.canonical).toBe('https://test.tymbox.cz/cs/')
      })

      it('generates correct canonical URL for sub-pages', async () => {
        const metadata = await generatePageMetadata({
          locale: 'sk',
          pagePath: '/pricing',
          namespace: 'common'
        })

        expect(metadata.alternates.canonical).toBe('https://test.tymbox.cz/sk/pricing')
      })

      it('handles localized paths correctly', async () => {
        const metadata = await generatePageMetadata({
          locale: 'cs',
          pagePath: '/tymbox-plus',
          namespace: 'common'
        })

        expect(metadata.alternates.canonical).toBe('https://test.tymbox.cz/cs/tymbox-plus')
      })
    })

    describe('Language Alternates Generation', () => {
      it('generates hreflang for all supported locales', async () => {
        const metadata = await generatePageMetadata({
          locale: 'cs',
          pagePath: '/',
          namespace: 'common'
        })

        expect(metadata.alternates.languages).toBeDefined()
        expect(metadata.alternates.languages['cs-CZ']).toBe('https://test.tymbox.cz/cs/')
        expect(metadata.alternates.languages['sk-SK']).toBe('https://test.tymbox.cz/sk/')
        expect(metadata.alternates.languages['en-US']).toBe('https://test.tymbox.cz/en/')
      })

      it('uses localized paths for different locales', async () => {
        const metadata = await generatePageMetadata({
          locale: 'cs',
          pagePath: '/online-dochazka',
          namespace: 'common'
        })

        expect(metadata.alternates.languages['cs-CZ']).toBe('https://test.tymbox.cz/cs/online-dochazka')
        expect(metadata.alternates.languages['sk-SK']).toBe('https://test.tymbox.cz/sk/online-dochadzka')
      })
    })

    describe('OpenGraph Metadata', () => {
      it('generates OpenGraph metadata correctly', async () => {
        const metadata = await generatePageMetadata({
          locale: 'cs',
          pagePath: '/',
          namespace: 'common'
        })

        expect(metadata.openGraph).toBeDefined()
        expect(metadata.openGraph.title).toContain('Týmbox Test Title CS')
        expect(metadata.openGraph.description).toBe('Týmbox Test Description CS')
        expect(metadata.openGraph.url).toBe('https://test.tymbox.cz/cs/')
        expect(metadata.openGraph.siteName).toBe('Týmbox')
        expect(metadata.openGraph.locale).toBe('cs-CZ')
        expect(metadata.openGraph.type).toBe('website')
      })

      it('includes OpenGraph images', async () => {
        const metadata = await generatePageMetadata({
          locale: 'cs',
          pagePath: '/',
          namespace: 'common'
        })

        expect(metadata.openGraph.images).toBeDefined()
        expect(metadata.openGraph.images[0].url).toBe('https://test.tymbox.cz/images/og-image.png')
        expect(metadata.openGraph.images[0].alt).toContain('Týmbox Test Title CS')
      })
    })

    describe('Twitter Metadata', () => {
      it('generates Twitter card metadata correctly', async () => {
        const metadata = await generatePageMetadata({
          locale: 'sk',
          pagePath: '/',
          namespace: 'common'
        })

        expect(metadata.twitter).toBeDefined()
        expect(metadata.twitter.card).toBe('summary_large_image')
        expect(metadata.twitter.title).toContain('Teambox Test Title SK')
        expect(metadata.twitter.description).toBe('Teambox Test Description SK')
        expect(metadata.twitter.images).toEqual(['https://test.tymbox.cz/images/og-image.png'])
      })
    })

    describe('Locale-Specific Behavior', () => {
      it('handles Czech locale correctly', async () => {
        const metadata = await generatePageMetadata({
          locale: 'cs',
          pagePath: '/',
          namespace: 'common'
        })

        expect(metadata.openGraph.siteName).toBe('Týmbox')
        expect(metadata.openGraph.locale).toBe('cs-CZ')
      })

      it('handles Slovak locale correctly', async () => {
        const metadata = await generatePageMetadata({
          locale: 'sk',
          pagePath: '/',
          namespace: 'common'
        })

        expect(metadata.openGraph.siteName).toBe('Teambox')
        expect(metadata.openGraph.locale).toBe('sk-SK')
      })

      it('handles English locale correctly', async () => {
        const metadata = await generatePageMetadata({
          locale: 'en',
          pagePath: '/',
          namespace: 'common'
        })

        expect(metadata.openGraph.siteName).toBe('Tymbox')
        expect(metadata.openGraph.locale).toBe('en-US')
      })

      it('falls back to Czech for unknown locales', async () => {
        const metadata = await generatePageMetadata({
          locale: 'unknown',
          pagePath: '/',
          namespace: 'common'
        })

        expect(metadata.openGraph.siteName).toBe('Týmbox')
      })
    })

    describe('Error Handling', () => {
      it('handles translation errors gracefully', async () => {
        mockGetTranslations.mockRejectedValueOnce(new Error('Translation failed'))
        
        const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

        const metadata = await generatePageMetadata({
          locale: 'cs',
          pagePath: '/',
          namespace: 'common'
        })

        expect(metadata).toBeDefined()
        expect(metadata.title).toBeDefined()
        expect(metadata.description).toBeDefined()
        expect(consoleSpy).toHaveBeenCalledWith(
          expect.stringContaining('Missing translations for locale cs'),
          expect.any(Error)
        )

        consoleSpy.mockRestore()
      })

      it('provides meaningful defaults when everything fails', async () => {
        mockGetTranslations.mockRejectedValueOnce(new Error('Complete failure'))

        const metadata = await generatePageMetadata({
          locale: 'cs',
          pagePath: '/',
          namespace: 'nonexistent'
        })

        expect(metadata.title).toContain('Týmbox')
        expect(metadata.description).toContain('docházkový systém')
        expect(metadata.alternates.canonical).toBe('https://test.tymbox.cz/cs/')
      })
    })

    describe('Custom Parameters', () => {
      it('uses custom default title and description', async () => {
        mockGetTranslations.mockImplementationOnce(async () => (key) => undefined)

        const metadata = await generatePageMetadata({
          locale: 'cs',
          pagePath: '/',
          namespace: 'common',
          defaultTitle: 'Custom Title',
          defaultDescription: 'Custom Description'
        })

        expect(metadata.title).toContain('Custom Title')
        expect(metadata.description).toBe('Custom Description')
      })
    })
  })

  describe('generateHreflangMetadata (Legacy Function)', () => {
    it('is an alias for generatePageMetadata', async () => {
      const options = {
        locale: 'cs',
        pagePath: '/',
        namespace: 'common'
      }

      const metadata1 = await generatePageMetadata(options)
      const metadata2 = await generateHreflangMetadata(options)

      expect(metadata1).toEqual(metadata2)
    })
  })

  describe('Localized Paths Mapping', () => {
    it('maps Czech attendance path correctly', async () => {
      const metadata = await generatePageMetadata({
        locale: 'cs',
        pagePath: '/online-dochazka',
        namespace: 'common'
      })

      expect(metadata.alternates.canonical).toBe('https://test.tymbox.cz/cs/online-dochazka')
      expect(metadata.alternates.languages['sk-SK']).toBe('https://test.tymbox.cz/sk/online-dochadzka')
    })

    it('maps Slovak booking path correctly', async () => {
      const metadata = await generatePageMetadata({
        locale: 'sk',
        pagePath: '/online-rezervace',
        namespace: 'common'
      })

      expect(metadata.alternates.canonical).toBe('https://test.tymbox.cz/sk/online-rezervacie')
      expect(metadata.alternates.languages['cs-CZ']).toBe('https://test.tymbox.cz/cs/online-rezervace')
    })

    it('handles unmapped paths correctly', async () => {
      const metadata = await generatePageMetadata({
        locale: 'cs',
        pagePath: '/unmapped-page',
        namespace: 'common'
      })

      expect(metadata.alternates.canonical).toBe('https://test.tymbox.cz/cs/unmapped-page')
      expect(metadata.alternates.languages['sk-SK']).toBe('https://test.tymbox.cz/sk/unmapped-page')
    })
  })
})