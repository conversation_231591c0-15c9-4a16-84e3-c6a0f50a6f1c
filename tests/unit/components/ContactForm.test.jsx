// ABOUTME: Comprehensive unit tests for ContactForm component functionality
// ABOUTME: Tests form rendering, validation, submission, loading states, and error handling

import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'

// Mock next-intl - must be defined before vi.mock
const mockTranslations = {
  'ContactForm.labelName': 'Name',
  'ContactForm.labelEmail': 'Email',
  'ContactForm.labelMessage': 'Message',
  'ContactForm.buttonSend': 'Send Message',
  'ContactForm.buttonSending': 'Sending...',
  'ContactForm.successMessage': 'Message sent successfully!',
  'ContactForm.errorMessage': 'Error sending message. Please try again.',
  'ContactForm.errorAllFieldsRequired': 'All fields are required.'
}

vi.mock('next-intl', () => ({
  useTranslations: vi.fn((namespace) => (key) => {
    const fullKey = `${namespace}.${key}`
    return mockTranslations[fullKey] || key
  })
}))

import ContactForm from '@/components/ContactForm'

// Mock fetch
global.fetch = vi.fn()

describe('ContactForm', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    fetch.mockClear()
  })

  describe('Component Rendering', () => {
    it('renders all form fields correctly', () => {
      render(<ContactForm />)
      
      expect(screen.getByTestId('contact-form')).toBeInTheDocument()
      expect(screen.getByTestId('name-input')).toBeInTheDocument()
      expect(screen.getByTestId('email-input')).toBeInTheDocument()
      expect(screen.getByTestId('message-input')).toBeInTheDocument()
      expect(screen.getByTestId('submit-button')).toBeInTheDocument()
    })

    it('displays correct labels', () => {
      render(<ContactForm />)
      
      expect(screen.getByText('Name')).toBeInTheDocument()
      expect(screen.getByText('Email')).toBeInTheDocument()
      expect(screen.getByText('Message')).toBeInTheDocument()
      expect(screen.getByText('Send Message')).toBeInTheDocument()
    })

    it('has required attributes on all inputs', () => {
      render(<ContactForm />)
      
      expect(screen.getByTestId('name-input')).toHaveAttribute('required')
      expect(screen.getByTestId('email-input')).toHaveAttribute('required')
      expect(screen.getByTestId('message-input')).toHaveAttribute('required')
    })

    it('sets correct input types', () => {
      render(<ContactForm />)
      
      expect(screen.getByTestId('name-input')).toHaveAttribute('type', 'text')
      expect(screen.getByTestId('email-input')).toHaveAttribute('type', 'email')
      expect(screen.getByTestId('submit-button')).toHaveAttribute('type', 'submit')
    })
  })

  describe('Form Validation', () => {
    it('shows validation error when submitting empty form', async () => {
      render(<ContactForm />)
      
      const submitButton = screen.getByTestId('submit-button')
      fireEvent.click(submitButton)
      
      await waitFor(() => {
        expect(screen.getByTestId('form-status')).toBeInTheDocument()
        expect(screen.getByText('All fields are required.')).toBeInTheDocument()
      })
    })

    it('shows validation error when name is missing', async () => {
      render(<ContactForm />)
      
      fireEvent.change(screen.getByTestId('email-input'), { target: { value: '<EMAIL>' }})
      fireEvent.change(screen.getByTestId('message-input'), { target: { value: 'Test message' }})
      fireEvent.click(screen.getByTestId('submit-button'))
      
      await waitFor(() => {
        expect(screen.getByText('All fields are required.')).toBeInTheDocument()
      })
    })

    it('shows validation error when email is missing', async () => {
      render(<ContactForm />)
      
      fireEvent.change(screen.getByTestId('name-input'), { target: { value: 'John Doe' }})
      fireEvent.change(screen.getByTestId('message-input'), { target: { value: 'Test message' }})
      fireEvent.click(screen.getByTestId('submit-button'))
      
      await waitFor(() => {
        expect(screen.getByText('All fields are required.')).toBeInTheDocument()
      })
    })

    it('shows validation error when message is missing', async () => {
      render(<ContactForm />)
      
      fireEvent.change(screen.getByTestId('name-input'), { target: { value: 'John Doe' }})
      fireEvent.change(screen.getByTestId('email-input'), { target: { value: '<EMAIL>' }})
      fireEvent.click(screen.getByTestId('submit-button'))
      
      await waitFor(() => {
        expect(screen.getByText('All fields are required.')).toBeInTheDocument()
      })
    })
  })

  describe('Form Input Handling', () => {
    it('updates name input value', () => {
      render(<ContactForm />)
      
      const nameInput = screen.getByTestId('name-input')
      fireEvent.change(nameInput, { target: { value: 'John Doe' }})
      
      expect(nameInput).toHaveValue('John Doe')
    })

    it('updates email input value', () => {
      render(<ContactForm />)
      
      const emailInput = screen.getByTestId('email-input')
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' }})
      
      expect(emailInput).toHaveValue('<EMAIL>')
    })

    it('updates message textarea value', () => {
      render(<ContactForm />)
      
      const messageInput = screen.getByTestId('message-input')
      fireEvent.change(messageInput, { target: { value: 'Test message content' }})
      
      expect(messageInput).toHaveValue('Test message content')
    })
  })

  describe('Form Submission', () => {
    const fillValidForm = () => {
      fireEvent.change(screen.getByTestId('name-input'), { target: { value: 'John Doe' }})
      fireEvent.change(screen.getByTestId('email-input'), { target: { value: '<EMAIL>' }})
      fireEvent.change(screen.getByTestId('message-input'), { target: { value: 'Test message' }})
    }

    it('shows loading state during submission', async () => {
      fetch.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)))
      
      render(<ContactForm />)
      fillValidForm()
      
      fireEvent.click(screen.getByTestId('submit-button'))
      
      // Should show loading state immediately
      expect(screen.getByText('Sending...')).toBeInTheDocument()
      expect(screen.getByTestId('submit-button')).toBeDisabled()
    })

    it('calls API with correct data on valid submission', async () => {
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true })
      })
      
      render(<ContactForm />)
      fillValidForm()
      
      fireEvent.click(screen.getByTestId('submit-button'))
      
      await waitFor(() => {
        expect(fetch).toHaveBeenCalledWith('/api/contact', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            name: 'John Doe',
            email: '<EMAIL>',
            message: 'Test message'
          })
        })
      })
    })

    it('shows success message and clears form on successful submission', async () => {
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true })
      })
      
      render(<ContactForm />)
      fillValidForm()
      
      fireEvent.click(screen.getByTestId('submit-button'))
      
      await waitFor(() => {
        expect(screen.getByText('Message sent successfully!')).toBeInTheDocument()
      })
      
      // Form should be cleared
      expect(screen.getByTestId('name-input')).toHaveValue('')
      expect(screen.getByTestId('email-input')).toHaveValue('')
      expect(screen.getByTestId('message-input')).toHaveValue('')
    })

    it('handles API error response', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        json: async () => ({ error: 'Server error occurred' })
      })
      
      render(<ContactForm />)
      fillValidForm()
      
      fireEvent.click(screen.getByTestId('submit-button'))
      
      await waitFor(() => {
        expect(screen.getByText('Server error occurred')).toBeInTheDocument()
      })
    })

    it('handles network error', async () => {
      fetch.mockRejectedValueOnce(new Error('Network error'))
      
      render(<ContactForm />)
      fillValidForm()
      
      fireEvent.click(screen.getByTestId('submit-button'))
      
      await waitFor(() => {
        expect(screen.getByText('Error sending message. Please try again.')).toBeInTheDocument()
      })
    })

    it('handles malformed JSON response gracefully', async () => {
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => { throw new Error('Invalid JSON') }
      })
      
      render(<ContactForm />)
      fillValidForm()
      
      fireEvent.click(screen.getByTestId('submit-button'))
      
      await waitFor(() => {
        expect(screen.getByText('Message sent successfully!')).toBeInTheDocument()
      })
    })
  })

  describe('Loading State Management', () => {
    it('disables submit button during loading', async () => {
      fetch.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)))
      
      render(<ContactForm />)
      fillValidForm()
      
      const submitButton = screen.getByTestId('submit-button')
      fireEvent.click(submitButton)
      
      expect(submitButton).toBeDisabled()
      expect(submitButton).toHaveClass('disabled:opacity-50')
    })

    it('re-enables submit button after submission completes', async () => {
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true })
      })
      
      render(<ContactForm />)
      fillValidForm()
      
      const submitButton = screen.getByTestId('submit-button')
      fireEvent.click(submitButton)
      
      await waitFor(() => {
        expect(submitButton).not.toBeDisabled()
        expect(screen.getByText('Send Message')).toBeInTheDocument()
      })
    })
  })

  describe('Error Message Styling', () => {
    it('applies error styling to error messages', async () => {
      render(<ContactForm />)
      
      fireEvent.click(screen.getByTestId('submit-button'))
      
      await waitFor(() => {
        const statusElement = screen.getByTestId('form-status')
        expect(statusElement).toHaveClass('text-red-500')
      })
    })

    it('applies success styling to success messages', async () => {
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true })
      })
      
      render(<ContactForm />)
      fillValidForm()
      
      fireEvent.click(screen.getByTestId('submit-button'))
      
      await waitFor(() => {
        const statusElement = screen.getByTestId('form-status')
        expect(statusElement).toHaveClass('text-green-600')
      })
    })
  })

  describe('Translation Integration', () => {
    it('calls useTranslations with correct namespace', async () => {
      const { useTranslations } = await import('next-intl')
      render(<ContactForm />)
      
      expect(useTranslations).toHaveBeenCalledWith('ContactForm')
    })

    it('uses translated text for all UI elements', () => {
      render(<ContactForm />)
      
      // Verify all translations are being used
      expect(screen.getByText('Name')).toBeInTheDocument()
      expect(screen.getByText('Email')).toBeInTheDocument()
      expect(screen.getByText('Message')).toBeInTheDocument()
      expect(screen.getByText('Send Message')).toBeInTheDocument()
    })
  })
})