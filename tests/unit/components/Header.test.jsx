// ABOUTME: Comprehensive unit tests for Header component navigation and mobile menu functionality
// ABOUTME: Tests logo display, navigation links, mobile menu behavior, and locale-specific login URLs

import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'

// Mock next/image
vi.mock('next/image', () => ({
  default: ({ src, alt, width, height, priority, ...props }) => (
    <img 
      src={src} 
      alt={alt} 
      width={width} 
      height={height}
      data-priority={priority}
      {...props}
    />
  )
}))

// Mock next-intl
const mockTranslations = {
  'header.attendance': 'Docházka',
  'header.bookings': 'Rezervace',
  'header.meetings': 'Schůzky',
  'header.pricing': 'Ceník',
  'header.login': 'Přihlášení',
  'header.menu': 'Menu',
  'paths.attendance': '/dochazka',
  'paths.bookings': '/rezervace',
  'paths.meetings': '/schuzky',
  'paths.pricing': '/cenik'
}

// Mock navigation module
vi.mock('@/i18n/navigation', () => ({
  Link: ({ href, children, className, onClick, role, ...props }) => (
    <a 
      href={href} 
      className={className}
      onClick={onClick}
      role={role}
      {...props}
    >
      {children}
    </a>
  ),
  usePathname: vi.fn(() => '/')
}))

vi.mock('next-intl', () => ({
  useTranslations: vi.fn((namespace) => (key) => {
    const fullKey = `${namespace}.${key}`
    return mockTranslations[fullKey] || key
  }),
  useLocale: vi.fn(() => 'cs')
}))

import Header from '@/components/layout/Header'

describe('Header', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Component Rendering', () => {
    it('renders header with logo', () => {
      render(<Header />)
      
      const logo = screen.getByAltText('Týmbox Logo')
      expect(logo).toBeInTheDocument()
      expect(logo).toHaveAttribute('src', '/logo.png')
      expect(logo).toHaveAttribute('width', '80')
      expect(logo).toHaveAttribute('height', '28')
    })

    it('renders logo as a link to home', () => {
      render(<Header />)
      
      const logoLink = screen.getByTestId('logo-link')
      expect(logoLink).toHaveAttribute('href', '/')
    })

    it('renders main navigation for desktop', () => {
      render(<Header />)
      
      const mainNav = screen.getByTestId('main-navigation')
      expect(mainNav).toBeInTheDocument()
      expect(mainNav).toHaveClass('hidden', 'md:block')
    })

    it('renders all navigation items', () => {
      render(<Header />)
      
      expect(screen.getByTestId('nav-attendance')).toBeInTheDocument()
      expect(screen.getByTestId('nav-bookings')).toBeInTheDocument()
      expect(screen.getByTestId('nav-meetings')).toBeInTheDocument()
      expect(screen.getByTestId('nav-pricing')).toBeInTheDocument()
    })

    it('renders login button for desktop', () => {
      render(<Header />)
      
      const loginButton = screen.getByTestId('login-button')
      expect(loginButton).toBeInTheDocument()
      expect(loginButton).toHaveClass('hidden', 'md:flex')
      expect(loginButton).toHaveTextContent('Přihlášení')
    })

    it('renders mobile menu toggle button', () => {
      render(<Header />)
      
      const mobileToggle = screen.getByTestId('mobile-menu-toggle')
      expect(mobileToggle).toBeInTheDocument()
      expect(mobileToggle).toHaveClass('md:hidden')
      expect(mobileToggle).toHaveTextContent('Menu')
    })
  })

  describe('Navigation Links', () => {
    it('renders navigation links with correct text and paths', () => {
      render(<Header />)
      
      expect(screen.getByTestId('nav-attendance')).toHaveTextContent('Docházka')
      expect(screen.getByTestId('nav-attendance')).toHaveAttribute('href', '/dochazka')
      
      expect(screen.getByTestId('nav-bookings')).toHaveTextContent('Rezervace')
      expect(screen.getByTestId('nav-bookings')).toHaveAttribute('href', '/rezervace')
      
      expect(screen.getByTestId('nav-meetings')).toHaveTextContent('Schůzky')
      expect(screen.getByTestId('nav-meetings')).toHaveAttribute('href', '/schuzky')
      
      expect(screen.getByTestId('nav-pricing')).toHaveTextContent('Ceník')
      expect(screen.getByTestId('nav-pricing')).toHaveAttribute('href', '/cenik')
    })

    it('applies correct CSS classes to navigation links', () => {
      render(<Header />)
      
      const navLink = screen.getByTestId('nav-attendance')
      expect(navLink).toHaveClass('font-bold', 'text-gray-700', 'hover:text-gray-900')
    })
  })

  describe('Locale-specific Login URLs', () => {
    it('renders Czech login URL for Czech locale', async () => {
      const { useLocale } = await import('next-intl')
      useLocale.mockReturnValue('cs')
      render(<Header />)
      
      const loginButton = screen.getByTestId('login-button')
      expect(loginButton).toHaveAttribute('href', 'https://app.tymbox.cz/cs/users/sign_in')
    })

    it('renders Slovak login URL for Slovak locale', async () => {
      const { useLocale } = await import('next-intl')
      useLocale.mockReturnValue('sk')
      render(<Header />)
      
      const loginButton = screen.getByTestId('login-button')
      expect(loginButton).toHaveAttribute('href', 'https://app.tymbox.cz/sk/users/sign_in')
    })

    it('falls back to Czech URL for unknown locale', async () => {
      const { useLocale } = await import('next-intl')
      useLocale.mockReturnValue('unknown')
      render(<Header />)
      
      const loginButton = screen.getByTestId('login-button')
      expect(loginButton).toHaveAttribute('href', 'https://app.tymbox.cz/cs/users/sign_in')
    })
  })

  describe('Mobile Menu Functionality', () => {
    it('mobile menu is hidden by default', () => {
      render(<Header />)
      
      expect(screen.queryByTestId('mobile-menu')).not.toBeInTheDocument()
    })

    it('opens mobile menu when toggle button is clicked', () => {
      render(<Header />)
      
      const toggleButton = screen.getByTestId('mobile-menu-toggle')
      fireEvent.click(toggleButton)
      
      expect(screen.getByTestId('mobile-menu')).toBeInTheDocument()
    })

    it('closes mobile menu when toggle button is clicked again', () => {
      render(<Header />)
      
      const toggleButton = screen.getByTestId('mobile-menu-toggle')
      
      // Open menu
      fireEvent.click(toggleButton)
      expect(screen.getByTestId('mobile-menu')).toBeInTheDocument()
      
      // Close menu
      fireEvent.click(toggleButton)
      expect(screen.queryByTestId('mobile-menu')).not.toBeInTheDocument()
    })

    it('renders all mobile navigation items when menu is open', () => {
      render(<Header />)
      
      fireEvent.click(screen.getByTestId('mobile-menu-toggle'))
      
      expect(screen.getByTestId('mobile-nav-attendance')).toBeInTheDocument()
      expect(screen.getByTestId('mobile-nav-bookings')).toBeInTheDocument()
      expect(screen.getByTestId('mobile-nav-meetings')).toBeInTheDocument()
      expect(screen.getByTestId('mobile-nav-pricing')).toBeInTheDocument()
      expect(screen.getByTestId('mobile-login-button')).toBeInTheDocument()
    })

    it('closes mobile menu when navigation item is clicked', () => {
      render(<Header />)
      
      // Open menu
      fireEvent.click(screen.getByTestId('mobile-menu-toggle'))
      expect(screen.getByTestId('mobile-menu')).toBeInTheDocument()
      
      // Click navigation item
      fireEvent.click(screen.getByTestId('mobile-nav-attendance'))
      
      expect(screen.queryByTestId('mobile-menu')).not.toBeInTheDocument()
    })

    it('closes mobile menu when mobile login is clicked', () => {
      render(<Header />)
      
      // Open menu
      fireEvent.click(screen.getByTestId('mobile-menu-toggle'))
      expect(screen.getByTestId('mobile-menu')).toBeInTheDocument()
      
      // Click mobile login
      fireEvent.click(screen.getByTestId('mobile-login-button'))
      
      expect(screen.queryByTestId('mobile-menu')).not.toBeInTheDocument()
    })

    it('renders overlay when mobile menu is open', () => {
      render(<Header />)
      
      fireEvent.click(screen.getByTestId('mobile-menu-toggle'))
      
      const overlay = document.querySelector('.fixed.inset-0.bg-gray-800\\/50')
      expect(overlay).toBeInTheDocument()
    })

    it('closes mobile menu when overlay is clicked', () => {
      render(<Header />)
      
      // Open menu
      fireEvent.click(screen.getByTestId('mobile-menu-toggle'))
      expect(screen.getByTestId('mobile-menu')).toBeInTheDocument()
      
      // Click overlay
      const overlay = document.querySelector('.fixed.inset-0.bg-gray-800\\/50')
      fireEvent.click(overlay)
      
      expect(screen.queryByTestId('mobile-menu')).not.toBeInTheDocument()
    })
  })

  describe('Click Outside Behavior', () => {
    it('closes mobile menu when clicking outside', async () => {
      render(<Header />)
      
      // Open menu
      fireEvent.click(screen.getByTestId('mobile-menu-toggle'))
      expect(screen.getByTestId('mobile-menu')).toBeInTheDocument()
      
      // Click outside (on document body)
      fireEvent.mouseDown(document.body)
      
      await waitFor(() => {
        expect(screen.queryByTestId('mobile-menu')).not.toBeInTheDocument()
      })
    })

    it('does not close menu when clicking inside menu', () => {
      render(<Header />)
      
      // Open menu
      fireEvent.click(screen.getByTestId('mobile-menu-toggle'))
      const mobileMenu = screen.getByTestId('mobile-menu')
      
      // Click inside menu
      fireEvent.mouseDown(mobileMenu)
      
      expect(screen.getByTestId('mobile-menu')).toBeInTheDocument()
    })

    it('does not close menu when clicking the toggle button', () => {
      render(<Header />)
      
      const toggleButton = screen.getByTestId('mobile-menu-toggle')
      
      // Open menu
      fireEvent.click(toggleButton)
      expect(screen.getByTestId('mobile-menu')).toBeInTheDocument()
      
      // Click toggle button (mousedown, not click)
      fireEvent.mouseDown(toggleButton)
      
      expect(screen.getByTestId('mobile-menu')).toBeInTheDocument()
    })
  })

  describe('Mobile Menu Styling', () => {
    it('applies correct classes to mobile menu items', () => {
      render(<Header />)
      
      fireEvent.click(screen.getByTestId('mobile-menu-toggle'))
      
      const mobileNavItem = screen.getByTestId('mobile-nav-attendance')
      expect(mobileNavItem).toHaveClass(
        'block', 'px-4', 'py-4', 'text-md', 'font-semibold', 
        'text-gray-700', 'hover:bg-gray-100', 'hover:text-gray-900', 'text-center'
      )
    })

    it('applies special styling to mobile login button', () => {
      render(<Header />)
      
      fireEvent.click(screen.getByTestId('mobile-menu-toggle'))
      
      const mobileLogin = screen.getByTestId('mobile-login-button')
      expect(mobileLogin).toHaveClass(
        'block', 'px-4', 'py-4', 'text-md', 'font-semibold', 
        'text-green-primary', 'hover:bg-gray-100', 'text-center'
      )
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA attributes on mobile menu', () => {
      render(<Header />)
      
      fireEvent.click(screen.getByTestId('mobile-menu-toggle'))
      
      const mobileMenu = screen.getByTestId('mobile-menu')
      expect(mobileMenu.querySelector('[role="menu"]')).toBeInTheDocument()
      expect(mobileMenu.querySelector('[aria-orientation="vertical"]')).toBeInTheDocument()
      expect(mobileMenu.querySelector('[aria-labelledby="options-menu"]')).toBeInTheDocument()
    })

    it('has menuitem role on mobile navigation links', () => {
      render(<Header />)
      
      fireEvent.click(screen.getByTestId('mobile-menu-toggle'))
      
      expect(screen.getByTestId('mobile-nav-attendance')).toHaveAttribute('role', 'menuitem')
      expect(screen.getByTestId('mobile-login-button')).toHaveAttribute('role', 'menuitem')
    })
  })

  describe('Translation Integration', () => {
    it('calls useTranslations with correct namespaces', async () => {
      const { useTranslations } = await import('next-intl')
      render(<Header />)
      
      expect(useTranslations).toHaveBeenCalledWith('header')
      expect(useTranslations).toHaveBeenCalledWith('paths')
    })

    it('uses translated text for navigation items', () => {
      render(<Header />)
      
      expect(screen.getByText('Docházka')).toBeInTheDocument()
      expect(screen.getByText('Rezervace')).toBeInTheDocument()
      expect(screen.getByText('Schůzky')).toBeInTheDocument()
      expect(screen.getByText('Ceník')).toBeInTheDocument()
    })
  })
})