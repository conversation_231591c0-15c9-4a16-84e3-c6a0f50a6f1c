// ABOUTME: E2E tests for navigation and locale switching functionality
// ABOUTME: Tests navigation between locales and internal page navigation

import { test, expect } from '@playwright/test'

test.describe('Navigation', () => {
  test('navigation links work correctly', async ({ page }) => {
    await page.goto('/cs')
    
    // Test navigation to pricing page if link exists
    const pricingLink = page.locator('a[href*="cenik"]').first()
    if (await pricingLink.isVisible({ timeout: 2000 })) {
      await pricingLink.click()
      await expect(page).toHaveURL(/\/cs\/cenik/)
    }
    
    // Navigate back to home
    await page.goto('/cs')
    
    // Test navigation to contact page if link exists
    const contactLink = page.locator('a[href*="kontakt"]').first()
    if (await contactLink.isVisible({ timeout: 2000 })) {
      await contactLink.click()
      await expect(page).toHaveURL(/\/cs\/kontakt/)
    }
    
    // Test navigation back to home
    const homeLink = page.locator('a[href="/cs"]').first()
    if (await homeLink.isVisible({ timeout: 2000 })) {
      await homeLink.click()
      await expect(page).toHaveURL(/\/cs$/)
    }
  })
  
  test('locale switching works correctly', async ({ page }) => {
    await page.goto('/cs')
    
    // Test switching to Slovak
    const slovakLink = page.locator('a[href*="/sk"]').first()
    if (await slovakLink.isVisible()) {
      await slovakLink.click()
      await expect(page).toHaveURL(/\/sk/)
    }
    
    // Test switching to English
    const englishLink = page.locator('a[href*="/en"]').first()
    if (await englishLink.isVisible()) {
      await englishLink.click()
      await expect(page).toHaveURL(/\/en/)
    }
  })
  
  test('registration redirects work correctly', async ({ page }) => {
    // Test Czech registration redirect
    await page.goto('/cs/registrace')
    await expect(page).toHaveURL(/app\.tymbox\.cz/)
    
    // Test Slovak registration redirect
    await page.goto('/sk/registracia')
    await expect(page).toHaveURL(/app\.tymbox\.cz/)
  })
  
  
  test('footer links work correctly', async ({ page }) => {
    await page.goto('/cs')
    
    // Test privacy policy link
    const privacyLink = page.locator('a[href*="privacy"]').first()
    if (await privacyLink.isVisible()) {
      await privacyLink.click()
      await expect(page).toHaveURL(/\/cs\/privacy/)
    }
  })
})