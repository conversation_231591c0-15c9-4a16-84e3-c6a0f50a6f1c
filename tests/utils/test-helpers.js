// ABOUTME: Test utilities and helpers for consistent testing setup
// ABOUTME: Provides common functions for locale testing and form interactions

import { render } from '@testing-library/react'

// Mock next-intl for testing
export const mockTranslations = {
  cs: {
    'common.home': 'Domů',
    'common.pricing': 'Ceník',
    'common.contact': 'Kontakt',
    'common.submit': 'Odeslat'
  },
  sk: {
    'common.home': 'Domov',
    'common.pricing': 'Ceník',
    'common.contact': 'Kontakt',
    'common.submit': 'Odoslať'
  },
  en: {
    'common.home': 'Home',
    'common.pricing': 'Pricing',
    'common.contact': 'Contact',
    'common.submit': 'Submit'
  }
}

// Helper to render components with locale context
export const renderWithLocale = (component, locale = 'cs') => {
  const MockIntlProvider = ({ children }) => {
    const t = (key) => mockTranslations[locale][key] || key
    return children
  }
  
  return render(<MockIntlProvider>{component}</MockIntlProvider>)
}

// Helper to get localized text
export const getLocalizedText = (key, locale = 'cs') => {
  return mockTranslations[locale][key] || key
}

// Helper to simulate form interactions
export const fillForm = async (page, formData) => {
  for (const [field, value] of Object.entries(formData)) {
    await page.fill(`[name="${field}"]`, value)
  }
}

// Helper to wait for navigation
export const waitForNavigation = async (page, expectedUrl) => {
  await page.waitForURL(expectedUrl)
}

// Helper to check SEO elements
export const checkSEOElements = async (page, expectedTitle) => {
  await page.locator('title').waitFor()
  const title = await page.title()
  return title.includes(expectedTitle)
}

// Helper to test locale switching
export const testLocaleSwitch = async (page, fromLocale, toLocale) => {
  await page.goto(`/${fromLocale}`)
  const localeLink = page.locator(`a[href*="/${toLocale}"]`).first()
  
  if (await localeLink.isVisible()) {
    await localeLink.click()
    await page.waitForURL(`**/${toLocale}**`)
    return true
  }
  
  return false
}

// Common test data
export const testFormData = {
  contact: {
    name: 'Test User',
    email: '<EMAIL>',
    message: 'This is a test message'
  },
  order: {
    company: 'Test Company',
    email: '<EMAIL>',
    phone: '+420123456789'
  }
}

// Helper to check if element is visible with timeout
export const waitForElement = async (page, selector, timeout = 5000) => {
  try {
    await page.locator(selector).waitFor({ timeout })
    return true
  } catch (error) {
    return false
  }
}