import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';
import { remark } from 'remark';
import html from 'remark-html';

export async function getLocalizedMarkdown(filePath: string, locale: string) {
  const fullPath = path.join(process.cwd(), 'src/app/[locale]', filePath, `${locale}.md`);
  
  try {
    if (!fs.existsSync(fullPath)) {
      throw new Error(`Markdown file not found at ${fullPath}`);
    }
    
    const fileContents = fs.readFileSync(fullPath, 'utf8');
    const { content, data } = matter(fileContents);
    
    const processedContent = await remark()
      .use(html)
      .process(content);
      
    const htmlContent = processedContent.toString();
    
    return {
      htmlContent,
      frontMatter: data,
    };
  } catch (error) {
    console.error(`Error loading markdown from ${fullPath}:`, error);
    throw error;
  }
}

export async function getLocalizedHtml(filePath: string, locale: string) {
  const fullPath = path.join(process.cwd(), 'src/app/[locale]', filePath, `${locale}.html`);

  try {
    if (!fs.existsSync(fullPath)) {
      // Fallback to default locale if specific locale not found
      const defaultLocalePath = path.join(process.cwd(), 'src/app/[locale]', filePath, `en.html`); // Assuming 'en' is default
      if (fs.existsSync(defaultLocalePath)) {
        console.warn(`HTML file not found at ${fullPath}, falling back to ${defaultLocalePath}`);
        const fileContents = fs.readFileSync(defaultLocalePath, 'utf8');
        return { htmlContent: fileContents };
      }
      throw new Error(`HTML file not found at ${fullPath} and no fallback available.`);
    }
    
    const fileContents = fs.readFileSync(fullPath, 'utf8');
    
    return {
      htmlContent: fileContents,
    };
  } catch (error) {
    console.error(`Error loading HTML from ${fullPath}:`, error);
    // It might be better to return a default error HTML or re-throw,
    // depending on how the consuming page should handle this.
    // For now, returning a simple error message within the content.
    return { htmlContent: `<p>Error loading content. Details: ${(error as Error).message}</p>` };
  }
} 