"use client";

import React, { createContext, useState, useContext, ReactNode } from 'react';
import Modal from '@/components/modal/Modal';
import OrderForm from '@/components/OrderForm';
import { useTranslations } from 'next-intl';

type ModalContextType = {
  openOrderModal: (planName?: string) => void;
  closeModal: () => void;
};

const ModalContext = createContext<ModalContextType | undefined>(undefined);

export function ModalProvider({ children }: { children: ReactNode }) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string>('');
  const t = useTranslations('common');

  const openOrderModal = (planName?: string) => {
    setSelectedPlan(planName || '');
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const handleOrderSuccess = () => {
    setTimeout(() => {
      closeModal();
    }, 3000);
  };

  return (
    <ModalContext.Provider value={{ openOrderModal, closeModal }}>
      {children}
      
      <Modal
        isOpen={isModalOpen}
        onClose={closeModal}
        title={t('modalTitle')}
      >
        <OrderForm 
          selectedPlan={selectedPlan}
          onSuccess={handleOrderSuccess}
        />
      </Modal>
    </ModalContext.Provider>
  );
}

export function useModal() {
  const context = useContext(ModalContext);
  if (context === undefined) {
    throw new Error('useModal must be used within a ModalProvider');
  }
  return context;
} 