import Image from "next/image";
import { Link } from '@/i18n/navigation';
import { setRequestLocale, getTranslations } from 'next-intl/server';
import { useTranslations } from 'next-intl';

// generateMetadata function for SEO
export async function generateMetadata({ params: { locale } }: { params: { locale: string } }) {
  const t = await getTranslations({ locale, namespace: 'RezervacePage' });

  let title = "Bookings - Online Booking System for Companies | Tymbox";
  let description = "Simple online booking system for managing rooms, equipment, and other company resources.";

  try {
    title = t('metaTitle');
    description = t('metaDescription');
  } catch (error) {
    console.error('Missing metadata translations for RezervacePage:', error);
  }

  const alternates = {
    languages: {
      'sk': '/sk/online-rezervacie',
      'cs': '/cs/online-rezervace',
      'x-default': '/cs/online-rezervace',
    },
  };

  return {
    title,
    description,
    alternates,
  };
}

export default function BookingsPage({ params: { locale } }: { params: { locale: string } }) {
  setRequestLocale(locale);
  const tRezervace = useTranslations('RezervacePage');

  return (
    <div className="min-h-screen bg-gray-50 pt-[84px]">
      <main className="mx-auto max-w-6xl px-4 py-4">
        <div className="rounded-3xl bg-white p-8 shadow-md">
          <h1 className="mb-4 text-center text-5xl font-black tracking-tight leading-none md:text-6xl">
            <span className="text-charcoal">{tRezervace('mainHeading.part1')}</span>
            <span className="text-charcoal"> {tRezervace('mainHeading.part2')}</span>
            <span className="text-blue"> {tRezervace('mainHeading.part3Online')} </span> 
          </h1>
          <h2 className="mx-auto mb-10 max-w-3xl text-center text-xl font-bold tracking-tight text-gray-700 md:text-2xl">
            {tRezervace('subtitle')}
          </h2>
          <div className="mt-12 md:flex justify-center">
            <div className="relative h-80 overflow-hidden rounded-lg shadow-lg">
              <div className="bg-white p-2 rounded-lg">
                <div className="aspect-[9/16] md:aspect-video rounded-md overflow-hidden relative p-2">
                  <div className="flex items-center justify-center md:hidden">
                    <Image src="/images/screen_booking_m.png" alt={tRezervace('imageAlt')} width={390} height={842} className="w-full" />
                  </div>
                  <div className="hidden md:flex items-center justify-center">
                    <Image src="/images/screen_booking.png" alt={tRezervace('imageAlt')} width={600} height={768} className="w-full" />
                  </div>
                </div>
              </div>
              <div className="image-gradient-overlay">
                <a
                  href={`/${locale}/registrace`}
                  className="rounded-full bg-blue px-5 py-5 text-md font-bold text-white shadow-lg hover:bg-blue-300"
                >
                  {tRezervace('cta.tryFree')}
                </a>
              </div>
            </div>
          </div>
        </div>
      </main>

      <section className="py-10">
        <div className="mx-auto max-w-6xl px-4">
          <div className="text-center mb-16">
            <h2 className="text-2xl md:text-4xl font-black mb-4 leading-none tracking-tight text-charcoal">
              {tRezervace('section1.title')}
            </h2>
            <p className="text-md md:text-lg text-gray-700 max-w-2xl mx-auto">
              {tRezervace('section1.text')}
            </p>
          </div>

          <div className="text-center mb-16">
            <h2 className="text-2xl md:text-4xl font-black mb-4 leading-none tracking-tight text-charcoal">
              {tRezervace('section2.title')}
            </h2>
            <p className="text-md md:text-lg text-gray-700 max-w-2xl mx-auto">
              {tRezervace('section2.text')}
            </p>
          </div>
          
          <div className="text-center mb-16">
            <h2 className="text-2xl md:text-4xl font-black mb-4 leading-none tracking-tight text-charcoal">
              {tRezervace('section3.title')}
            </h2>
            <p className="text-md md:text-lg text-gray-700 max-w-2xl mx-auto">
              {tRezervace('section3.text')} 
            </p>
          </div>
        </div>

        <div className="text-center mb-16">
          <h2 className="text-2xl md:text-4xl font-black mb-4 leading-none tracking-tight text-charcoal">
            {tRezervace('section4.title')}
          </h2>
          <p className="text-md md:text-lg text-gray-700 max-w-2xl mx-auto">
            {tRezervace('section4.text')} 
          </p>
        </div>

        <div className="flex justify-center mt-16 mb-8">
          <div className="p-8 max-w-2xl w-full flex flex-col items-center">
            <p className="text-md text-xl text-bold text-blue mb-6 text-center">
              {tRezervace('cta_end.startUsingSubtitle')}
            </p>
            <a
              href={`/${locale}/registrace`}
              className="text-link px-8 py-4 text-2xl font-bold text-blue"
              >
              {tRezervace('cta_end.startUsing')}
            </a>
          </div>
        </div>

      </section>
    </div>
  );
} 