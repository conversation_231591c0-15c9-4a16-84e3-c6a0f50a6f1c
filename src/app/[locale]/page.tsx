import Image from "next/image";
import { Link } from '@/i18n/navigation';
import {useTranslations} from 'next-intl';
import {setRequestLocale, getTranslations} from 'next-intl/server';

// Component to generate hreflang links
// This would typically be more dynamic, fetching available locales
// const HreflangLinks = ({currentLocale}: {currentLocale: string}) => {
//   const locales = ['en', 'sk', 'cs'];
//   const currentPath = '';
//   return (
//     <>
//       {locales.map(locale => (
//         <link
//           key={locale}
//           rel="alternate"
//           hrefLang={locale}
//           href={`/${locale}${currentPath}`}
//         />
//       ))}
//       {/* Add x-default for non-specific language users if desired */}
//       {/* <link rel="alternate" hrefLang="x-default" href={`/en${currentPath}`} /> */}
//     </>
//   );
// };

export default function Home({ params: { locale } }: { params: { locale: string } }) {
  // Set the locale for this request
  setRequestLocale(locale);

  const t = useTranslations('common');
  const tHome = useTranslations('HomePage');

  return (
    <>
      {/* Hreflang links are now handled by generateMetadata */}
      {/* <HreflangLinks currentLocale={locale} /> */}

      <div className="min-h-screen bg-gray-50 pt-[84px]">
        <div className="mx-auto max-w-6xl px-4 py-4">
          <div className="rounded-3xl bg-white p-8 shadow-md">
            <h1 className="mb-4 text-center text-5xl font-black tracking-tight leading-none md:text-6xl">
              
              <span className="text-charcoal">{t('title')}</span>
              <span className="text-green-primary"> {tHome('mainHeading.online')}</span>
              <br />
              <span className="text-charcoal">{tHome('mainHeading.free')}</span>
              <span className="text-green-primary"></span>
            </h1>
            <h2 className="mx-auto mb-10 max-w-3xl text-center text-xl font-bold tracking-tight text-gray-700 md:text-2xl">
              {tHome('subtitle')}
            </h2>
            <div className="mt-12 flex justify-center">
              <div className="relative h-80 overflow-hidden rounded-lg shadow-lg">
                <div className="bg-white p-2 rounded-lg">
                  <div className="aspect-[9/16] md:aspect-video rounded-md overflow-hidden relative p-2">
                    <div className="flex items-center justify-center md:hidden">
                      <Image src="/images/screen_mobile.png" alt="týmbox" width={390} height={842} className="w-full" priority />
                    </div>
                    <div className="hidden md:flex items-center justify-center">
                      <Image src="/images/screen_desktop.png" alt="týmbox" width={600} height={768} className="w-full" priority />
                    </div>
                  </div>
                </div>
                <div className="image-gradient-overlay">
                  <a
                    href={`/${locale}/registrace`} 
                    className="rounded-full bg-green-primary px-5 py-5 text-md font-bold text-white shadow-lg hover:bg-green-secondary"
                  >
                    {tHome('cta.startUsing')}
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <section className="py-10">
          <div className="mx-auto max-w-6xl px-4">
            <div className="text-center mb-16">
              <h2 className="text-2xl md:text-4xl font-black mb-4 leading-none tracking-tight text-charcoal">
                {tHome('section1.title')}
              </h2>
              <p className="text-md md:text-lg text-gray-700 max-w-2xl mx-auto">
                {tHome('section1.text')}
              </p>
            </div>
            <div className="text-center mb-16">
              <h2 className="text-2xl md:text-4xl font-black mb-4 leading-none tracking-tight text-charcoal">
                {tHome('section2.title')}
              </h2>
              <p className="text-md md:text-lg text-gray-700 max-w-2xl mx-auto">
                {tHome('section2.text')}
              </p>
            </div>
            <div className="text-center mb-16">
              <h2 className="text-2xl md:text-4xl font-black mb-4 leading-none tracking-tight text-charcoal">
                {tHome('section3.title')}
              </h2>
              <p className="text-md md:text-lg text-gray-700 max-w-2xl mx-auto">
                {tHome('section3.text')}
              </p>
            </div>
            <div className="text-center mb-16">
              <h2 className="text-2xl md:text-4xl font-black mb-4 leading-none tracking-tight text-charcoal">
                {tHome('section4.title')}
              </h2>
              <p className="text-md md:text-lg text-gray-700 max-w-2xl mx-auto">
                {tHome('section4.text')}
              </p>
            </div>
            {/* CTA Section Start */}
            <div className="flex justify-center">
              <div className="bg-green-light rounded-2xl shadow-lg p-8 max-w-2xl w-full flex flex-col items-center">
                <h3 className="text-2xl md:text-3xl font-black text-charcoal mb-2 text-center">
                  {tHome('ctaSection.title')}
                </h3>
                <p className="text-md md:text-lg text-gray-700 mb-6 text-center">
                  {tHome('ctaSection.subtitle')}
                </p>
                <a
                  href={`/${locale}/registrace`}
                  className="rounded-full bg-green-primary px-8 py-4 text-lg font-bold text-white shadow-lg hover:bg-green-secondary transition-colors duration-200"
                >
                  {tHome('ctaSection.button')}
                </a>
              </div>
            </div>
            {/* CTA Section End */}
          </div>
        </section>
        
      </div>
    </>
  );
}

// Import the shared metadata generator
import { generatePageMetadata } from '@/lib/metadata';

// Function to generate Metadata for SEO
export async function generateMetadata({ params: { locale } }: { params: { locale: string } }) {
  return generatePageMetadata({
    locale,
    pagePath: '', 
    namespace: 'common',
    defaultTitle: 'Týmbox - Attendance System',
    defaultDescription: 'Týmbox - Simple online attendance system.',
  });
} 