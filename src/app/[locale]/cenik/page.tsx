import { setRequestLocale, getTranslations } from 'next-intl/server';
import PricingContent from '@/components/pricing/PricingContent';
import { generatePageMetadata } from '@/lib/metadata';

// generateMetadata function for SEO
export async function generateMetadata() {
  // In single-locale deployment, we get locale from routing config
  // The locale is determined by the build-time routing configuration
  const pagePath = '';
  
  return generatePageMetadata({
    
    pagePath,
    namespace: 'CenikPage',
    defaultTitle: "Pricing - Tymbox | Attendance and Online Booking System", 
    defaultDescription: "Tymbox services pricing - attendance system, booking system, and meeting planner for your company."
  });
}

export default function PricingPage({ params: { locale } }: { params: { locale: string } }) {
  setRequestLocale(locale);
  // In single-locale deployment, we get locale from routing config
  // The locale is determined by the build-time routing configuration
  // setRequestLocale removed - locale detected at runtime
  
  return <PricingContent />;
} 