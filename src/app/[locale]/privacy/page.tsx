import { setRequestLocale, getTranslations } from 'next-intl/server';
import { getServerLocale } from '@/lib/get-server-locale';
import { getLocalizedHtml } from '@/utils/markdown'; // Changed from getLocalizedMarkdown
import MarkdownContent from '@/components/MarkdownContent';
// Removed Link and useTranslations as they are not used in the simplified version

import { generatePageMetadata } from '@/lib/metadata';

export async function generateMetadata() {
  return generatePageMetadata({
    pagePath: '/privacy',
    namespace: 'PrivacyPage',
    defaultTitle: 'Privacy Policy - Týmbox',
    defaultDescription: 'Read the Týmbox privacy policy to understand how we handle your data.'
  });
}

export default async function PrivacyPage() {
  // Get locale for content
  const locale = await getServerLocale();
  
  // Get the localized HTML content
  const { htmlContent } = await getLocalizedHtml('privacy', locale);
  // Removed const t = await getTranslations({locale, namespace: 'common'}); as it's not used
  
  return (
    <div className="min-h-screen bg-gray-50 pt-[84px]">
        <main className="mx-auto max-w-2xl px-4 py-4">
            <div className="terms">
                <MarkdownContent htmlContent={htmlContent} />
            </div>
        </main>    
    </div>
  );
} 