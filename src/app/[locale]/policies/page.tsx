import { setRequestLocale, getTranslations } from 'next-intl/server';
import { getServerLocale } from '@/lib/get-server-locale';
import { getLocalizedHtml } from '@/utils/markdown';
import MarkdownContent from '@/components/MarkdownContent';

import { generatePageMetadata } from '@/lib/metadata';

export async function generateMetadata() {
  return generatePageMetadata({
    pagePath: '/policies',
    namespace: 'PoliciesPage',
    defaultTitle: 'Terms and Conditions - Týmbox',
    defaultDescription: 'Read the Týmbox terms and conditions to understand our policies.'
  });
}

export default async function PoliciesPage() {
  // Get locale for content
  const locale = await getServerLocale();
  
  const { htmlContent } = await getLocalizedHtml('policies', locale);
  
  return (
    <div className="min-h-screen bg-gray-50 pt-[84px]">
        <main className="mx-auto max-w-2xl px-4 py-4">
            <div className="terms">
                <MarkdownContent htmlContent={htmlContent} />
            </div>
        </main>    
    </div>
  );
} 