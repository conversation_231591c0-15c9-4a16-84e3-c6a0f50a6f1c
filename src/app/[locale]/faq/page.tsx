import { setRequestLocale } from 'next-intl/server';
import { useTranslations } from 'next-intl';

export default function FAQPage({ params: { locale } }: { params: { locale: string } }) {
  setRequestLocale(locale);
  const t = useTranslations('common');
  
  return (
    <div className="min-h-screen bg-gray-50 pt-[84px]">
      <div className="mx-auto max-w-4xl px-4 py-12">
        <div className="rounded-3xl bg-white p-8 shadow-md">
          <h1 className="mb-8 text-4xl font-black text-charcoal">
            {t('faq.title', { default: 'Často kladen<PERSON>' })}
          </h1>
          <div className="space-y-6">
            <div className="border-b border-gray-200 pb-4">
              <h2 className="text-xl font-bold text-charcoal mb-2">
                {t('faq.question1', { default: '<PERSON><PERSON> za<PERSON><PERSON> používat Týmbox?' })}
              </h2>
              <p className="text-gray-700">
                {t('faq.answer1', { default: 'Stač<PERSON> se registrovat a můžete začít používat náš systém zdarma.' })}
              </p>
            </div>
            {/* Add more FAQ items as needed */}
          </div>
        </div>
      </div>
    </div>
  );
}