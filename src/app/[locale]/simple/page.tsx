import { setRequestLocale, getTranslations } from 'next-intl/server';
import { getLocalizedMarkdown } from '@/utils/markdown';
import MarkdownContent from '@/components/MarkdownContent';
import { Link } from '@/i18n/navigation';
import { useTranslations } from 'next-intl';

import { getServerLocale } from '@/lib/get-server-locale';
import { generatePageMetadata } from '@/lib/metadata';

export async function generateMetadata({ params: { locale } }: { params: { locale: string } }) {
  const t = await getTranslations({ locale, namespace: 'common' });

  let title = "Simple Online Attendance - Týmbox"; 
  let description = "Free online attendance system created for small businesses without unnecessary complications."; 

  try {
    title = t('metaTitle');
    description = t('metaDescription');
  } catch (error) {
    console.error('Missing metadata translations for SimplePage:', error);
  }

  const alternates = {
    languages: {
      'en': '/en/simple',
      'sk': '/sk/simple',
      'cs': '/cs/simple',
      'x-default': '/en/simple',
    },
  };

  return {
    title,
    description,
    alternates,
  };
}

export default async function SimplePage({ params: { locale } }: { params: { locale: string } }) {
  setRequestLocale(locale);
  
  const { htmlContent } = await getLocalizedMarkdown('simple', locale);
  const t = await getTranslations({ locale, namespace: 'common' });
  
  return (
    <div className="min-h-screen bg-gray-50 pt-[84px]">
        <main className="mx-auto max-w-2xl px-4 py-4">
            <MarkdownContent htmlContent={htmlContent} />
            
            <div className="flex justify-center mt-16 mb-8">
              <div className="bg-green-light rounded-2xl shadow-lg p-8 max-w-2xl w-full flex flex-col items-center">
                <h3 className="text-2xl md:text-3xl font-black text-charcoal mb-2 text-center">
                  {t('ctaTitle3')}
                </h3>
                <p className="text-md md:text-lg text-gray-700 mb-6 text-center">
                  {t('ctaSubtitle3')}
                </p>
                            <a
              href={`/${locale}/registrace`}
              className="rounded-full bg-green-primary px-8 py-4 text-lg font-bold text-white shadow-lg hover:bg-green-secondary transition-colors duration-200"
            >
              {t('getStarted3')}
            </a>
              </div>
            </div>
        </main>    
    </div>
  );
} 