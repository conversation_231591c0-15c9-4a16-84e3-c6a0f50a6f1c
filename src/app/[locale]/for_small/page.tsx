import { setRequestLocale, getTranslations } from 'next-intl/server';
import { getLocalizedMarkdown } from '@/utils/markdown';
import MarkdownContent from '@/components/MarkdownContent';
import { generatePageMetadata } from '@/lib/metadata';
import { BASE_URL, getAllLocales } from '@/config/domains';
import { Metadata } from 'next';

type Props = {
  params: { locale: string };
};

export async function generateMetadata({ params: { locale } }: Props): Promise<Metadata> {
  return generatePageMetadata({
    locale,
    pagePath: '/for_small',
    namespace: 'common',
    defaultTitle: 'Free Online Attendance for Small Businesses - Týmbox',
    defaultDescription: 'Simple online attendance tracking designed for small businesses and teams.'
  });
}

export default async function ForSmallPage({ params: { locale } }: { params: { locale: string } }) {
  // In single-locale deployment, we get locale from routing config
  // The locale is determined by the build-time routing configuration
  setRequestLocale(locale);
  
  const { htmlContent } = await getLocalizedMarkdown('for_small', locale);
  const t = await getTranslations({ locale, namespace: 'common' });
  
  return (
    <div className="min-h-screen bg-gray-50 pt-[84px]">
        <main className="mx-auto max-w-2xl px-4 py-4">
            <MarkdownContent htmlContent={htmlContent} />
            
            <div className="flex justify-center mt-16 mb-8">
              <div className="bg-green-light rounded-2xl shadow-lg p-8 max-w-2xl w-full flex flex-col items-center">
                <h3 className="text-2xl md:text-3xl font-black text-charcoal mb-2 text-center">
                  {t('ctaTitle4')}
                </h3>
                <p className="text-md md:text-lg text-gray-700 mb-6 text-center">
                  {t('ctaSubtitle4')}
                </p>
                <a
                  href={`/${locale}/registrace`}
                  className="rounded-full bg-green-primary px-8 py-4 text-lg font-bold text-white shadow-lg hover:bg-green-secondary transition-colors duration-200"
                >
                  {t('getStarted4')}
                </a>
              </div>
            </div>
        </main>    
    </div>
  );
} 