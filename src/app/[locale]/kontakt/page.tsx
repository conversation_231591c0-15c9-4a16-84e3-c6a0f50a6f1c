import { setRequestLocale, getTranslations } from 'next-intl/server';
import { useTranslations } from 'next-intl';
import ContactForm from '@/components/ContactForm';

// generateMetadata function for SEO
export async function generateMetadata() {
  const t = await getTranslations('ContactPage');

  let title = "Contact Us - Týmbox";
  let description = "Get in touch with Tymbox. Send us your questions or feedback.";

  try {
    title = t('metaTitle');
    description = t('metaDescription');
  } catch (error) {
    console.error('Missing metadata translations for ContactPage:', error);
  }

  const alternates = {
    languages: {
      'en': '/en/kontakt',
      'sk': '/sk/kontakt',
      'cs': '/cs/kontakt',
      'x-default': '/en/kontakt',
    },
  };

  return {
    title,
    description,
    alternates,
  };
}

export default function ContactPage({ params: { locale } }: { params: { locale: string } }) {
  setRequestLocale(locale);
  // In single-locale deployment, we get locale from routing config
  // The locale is determined by the build-time routing configuration
  // setRequestLocale removed - locale detected at runtime
  const t = useTranslations('ContactPage');

  return (
    <div className="min-h-screen bg-gray-50 pt-24 md:pt-32">
      <section className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-12 md:mb-16">
          <h1 className="mb-4 text-4xl font-black tracking-tight text-charcoal md:text-5xl">
            {t('mainHeading')}
          </h1>
          <p className="mx-auto max-w-2xl text-lg text-gray-700 md:text-xl">
            {t('subtitle')}
          </p>
        </div>
        <ContactForm />
      </section>
    </div>
  );
} 