import { setRequestLocale, getTranslations } from 'next-intl/server';
import { getLocalizedMarkdown } from '@/utils/markdown';
import MarkdownContent from '@/components/MarkdownContent';
import { Link } from '@/i18n/navigation';
import { generatePageMetadata } from '@/lib/metadata';

export async function generateMetadata({ params: { locale } }: { params: { locale: string } }) {
  // In single-locale deployment, we get locale from routing config
  // The locale is determined by the build-time routing configuration
  const t = await getTranslations({ locale, namespace: 'common' });

  let title = "Online Attendance Tracking Without Installation - Týmbox"; 
  let description = "Learn why web apps are sometimes better for attendance tracking."; 

  try {
    title = t('metaTitle');
    description = t('metaDescription');
  } catch (error) {
    console.error('Missing metadata translations for NoInstallPage:', error);
  }

  const alternates = {
    languages: {
      'en': '/en/no_install',
      'sk': '/sk/mobilna-dochadzka-bez-instalacie',
      'cs': '/cs/mobilni-dochazka-bez-instalace',
      'x-default': '/cs/mobilni-dochazka-bez-instalace',
    },
  };

  return {
    title,
    description,
    alternates,
  };
}

export default async function NoInstallPage({ params: { locale } }: { params: { locale: string } }) {
  // In single-locale deployment, we get locale from routing config
  // The locale is determined by the build-time routing configuration
  setRequestLocale(locale);
  
  const { htmlContent } = await getLocalizedMarkdown('no_install', locale);
  const t = await getTranslations({ locale, namespace: 'common' });

  return (
    <div className="min-h-screen bg-gray-50 pt-[84px]">
      <main className="mx-auto max-w-2xl px-4 py-4">
        <MarkdownContent htmlContent={htmlContent} />
        
        <div className="flex justify-center mt-16 mb-8">
          <div className="bg-green-light rounded-2xl shadow-lg p-8 max-w-2xl w-full flex flex-col items-center">
            <h3 className="text-2xl md:text-3xl font-black text-charcoal mb-2 text-center">
              {t('ctaTitle2')}
            </h3>
            <p className="text-md md:text-lg text-gray-700 mb-6 text-center">
              {t('ctaSubtitle2')}
            </p>
            <a
              href={`/${locale}/registrace`}
              className="rounded-full bg-green-primary px-8 py-4 text-lg font-bold text-white shadow-lg hover:bg-green-secondary transition-colors duration-200"
            >
              {t('getStarted2')}
            </a>
          </div>
        </div>
      </main>    
    </div>
  );
} 