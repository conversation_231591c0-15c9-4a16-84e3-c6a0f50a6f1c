import { NextResponse } from 'next/server';
import { BASE_URL } from '@/config/domains';

export async function GET(request) {
  // Use the single base URL for all requests
  const baseUrl = BASE_URL;
  
  // Generate robots.txt content
  const robotsTxt = `# Robots.txt for Tymbox
User-agent: *
Allow: /

# Sitemap location
Sitemap: ${baseUrl}/sitemap.xml
Sitemap: ${baseUrl}/sitemap-0.xml

# Crawl-delay (optional, adjust as needed)
Crawl-delay: 1

# Disallow administrative paths
Disallow: /api/
Disallow: /_next/
Disallow: /404
Disallow: /500

# Allow search engines to index everything else
Allow: /images/
Allow: /*.js$
Allow: /*.css$
`;

  return new NextResponse(robotsTxt, {
    headers: {
      'Content-Type': 'text/plain',
      'Cache-Control': 'public, max-age=3600',
    },
  });
}