import { Resend } from 'resend';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    const { name, email, message } = await req.json();

    // Validate the input
    if (!name || !email || !message) {
      return NextResponse.json({ error: 'All fields are required' }, { status: 400 });
    }

    // Check if Resend API key is available
    const resendApiKey = process.env.RESEND_API_KEY;
    if (!resendApiKey) {
      console.log('Server error. Contact: <EMAIL>');
      return NextResponse.json({ error: 'Email service not configured' }, { status: 500 });
    }

    // Initialize Resend with your API key from environment variables
    const resend = new Resend(resendApiKey);

    // Construct email content
    const fromEmail = process.env.FROM_EMAIL || '<EMAIL>';
    const toEmail = process.env.RECIPIENT_EMAIL;

    if (!toEmail) {
        console.log('Server error. Contact: <EMAIL>' );
        return NextResponse.json({ error: 'Server configuration error' }, { status: 500 });
    }
    
    const subject = `New Contact Form Submission from ${name}`;
    const emailText = `
Name: ${name}
Email: ${email}

Message:
${message}
    `;
    const emailHtml = `
<h3>New Contact Form Submission</h3>
<p><strong>Name:</strong> ${name}</p>
<p><strong>Email:</strong> ${email}</p>
<p><strong>Message:</strong></p>
<p>${message.replace(/\n/g, '<br>')}</p>
    `;

    // Send email using Resend
    const { data, error } = await resend.emails.send({
      from: fromEmail,
      to: toEmail,
      subject: subject,
      replyTo: email,
      text: emailText,
      html: emailHtml,
    });

    if (error) {
      console.error('Error sending email via Resend:', error);
      return NextResponse.json({ error: 'Failed to send email' }, { status: 500 });
    }

    return NextResponse.json({ success: true, data }, { status: 200 });
  } catch (error) {
    console.error('Error processing contact form submission:', error);
    
    // Ensure we always return a proper JSON response
    if (error instanceof Error) {
      return NextResponse.json({ 
        error: 'Failed to send email', 
        details: error.message 
      }, { status: 500 });
    }
    
    return NextResponse.json({ 
      error: 'Failed to send email' 
    }, { status: 500 });
  }
} 