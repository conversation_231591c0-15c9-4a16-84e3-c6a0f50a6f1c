import { Resend } from 'resend';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  const resend = new Resend(process.env.RESEND_API_KEY);

  try {
    const { name, email, phone, companyName, selectedPlan, message } = await req.json();

    // Basic validation
    if (!name || !email || !selectedPlan) {
      return NextResponse.json({ error: 'Name, email, and selected plan are required.' }, { status: 400 });
    }

    const fromEmail = process.env.FROM_EMAIL || '<EMAIL>';
    const recipientEmail = process.env.RECIPIENT_EMAIL; // Where order notifications go

    if (!recipientEmail) {
      console.error('RECIPIENT_EMAIL environment variable is not set for orders.');
      return NextResponse.json({ error: 'Server configuration error' }, { status: 500 });
    }

    const subject = `New Plan Order Request: ${selectedPlan} from ${name}`;
    const replyTo = email;
    
    const textBody = `
New Order Request:
Plan: ${selectedPlan}
Name: ${name}
Email: ${email}
${phone ? `Phone: ${phone}` : ''}
${companyName ? `Company: ${companyName}` : ''}

Message:
${message || 'No additional message provided.'}
    `;

    const htmlBody = `
<h3>New Order Request</h3>
<p><strong>Plan:</strong> ${selectedPlan}</p>
<p><strong>Name:</strong> ${name}</p>
<p><strong>Email:</strong> ${email}</p>
${phone ? `<p><strong>Phone:</strong> ${phone}</p>` : ''}
${companyName ? `<p><strong>Company:</strong> ${companyName}</p>` : ''}
<p><strong>Message:</strong></p>
<p>${message ? message.replace(/\n/g, '<br>') : 'No additional message provided.'}</p>
    `;

    const { data, error } = await resend.emails.send({
      from: fromEmail,
      to: recipientEmail,
      subject: subject,
      replyTo: replyTo,
      text: textBody,
      html: htmlBody,
    });

    if (error) {
      console.error('Error sending order email via Resend:', error);
      return NextResponse.json({ error: 'Failed to send order email' }, { status: 500 });
    }

    return NextResponse.json({ success: true, data }, { status: 200 });
  } catch (error) {
    console.error('Error processing order submission:', error);
    return NextResponse.json({ error: 'Failed to process order' }, { status: 500 });
  }
} 