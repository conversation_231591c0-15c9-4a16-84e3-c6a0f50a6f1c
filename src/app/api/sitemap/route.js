import { NextResponse } from 'next/server';

// Define path mappings for all locales
const pathMappings = {
  'dochazka': {
    cs: '/online-dochazka',
    sk: '/online-dochadzka',
    en: '/attendance'
  },
  'rezervace': {
    cs: '/online-rezervace',
    sk: '/online-rezervacie',
    en: '/bookings'
  },
  'schuzky': {
    cs: '/planovac-schuzek',
    sk: '/planovac-stretnuti',
    en: '/meetings'
  },
  'cenik': {
    cs: '/tymbox-plus',
    sk: '/tymbox-plus',
    en: '/pricing'
  },
  'no_install': {
    cs: '/mobilni-dochazka-bez-instalace',
    sk: '/mobilna-dochadzka-bez-instalacie',
    en: '/no-install'
  },
  'why_free': {
    cs: '/online-dochazka-zdarma',
    sk: '/online-dochadzka-zadarmo',
    en: '/free-attendance'
  },
  'for_small': {
    cs: '/pro-male-firmy',
    sk: '/pre-male-firmy',
    en: '/for-small-business'
  },
  'simple': {
    cs: '/dochazka-bez-skoleni',
    sk: '/dochadzka-bez-skoleni',
    en: '/simple-attendance'
  },
  'faq': {
    cs: '/caste-dotazy',
    sk: '/caste-otazky',
    en: '/faq'
  },
  'policies': {
    cs: '/podminky-sluzby',
    sk: '/podmienky-sluzby',
    en: '/terms'
  },
  'privacy': {
    cs: '/ochrana-dat',
    sk: '/ochrana-dat',
    en: '/privacy'
  },
  'kontakt': {
    cs: '/kontakt',
    sk: '/kontakt',
    en: '/contact'
  }
};

export async function GET(request) {
  const baseUrl = 'https://www.tymbox.cz';
  const locales = ['cs', 'sk', 'en'];
  
  // List of all public pages to include in sitemap
  const pageKeys = [
    '', // Homepage
    'dochazka',
    'rezervace',
    'schuzky',
    'cenik',
    'no_install',
    'why_free',
    'for_small',
    'simple',
    'faq',
    'policies',
    'privacy',
    'kontakt'
  ];

  // XML header
  let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
  xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" ';
  xml += 'xmlns:xhtml="http://www.w3.org/1999/xhtml">\n';

  // For each page, generate URL entries for all locales
  for (const pageKey of pageKeys) {
    for (const locale of locales) {
      // Get localized path for current locale
      const pagePath = pageKey === '' ? '' : 
        (pathMappings[pageKey] && pathMappings[pageKey][locale]) || 
        `/${pageKey}`;
      
      const fullUrl = `${baseUrl}/${locale}${pagePath}`;

      // Start URL entry
      xml += '  <url>\n';
      xml += `    <loc>${fullUrl}</loc>\n`;
      
      // Add hreflang tags for all locales
      locales.forEach(altLocale => {
        const altPath = pageKey === '' ? '' : 
          (pathMappings[pageKey] && pathMappings[pageKey][altLocale]) || 
          `/${pageKey}`;
        const altUrl = `${baseUrl}/${altLocale}${altPath}`;
        const langCode = altLocale === 'cs' ? 'cs-CZ' : altLocale === 'sk' ? 'sk-SK' : 'en-US';
        xml += `    <xhtml:link rel="alternate" hreflang="${langCode}" href="${altUrl}" />\n`;
      });
      
      // Add x-default pointing to Czech version
      const czechPath = pageKey === '' ? '' : 
        (pathMappings[pageKey] && pathMappings[pageKey]['cs']) || 
        `/${pageKey}`;
      xml += `    <xhtml:link rel="alternate" hreflang="x-default" href="${baseUrl}/cs${czechPath}" />\n`;
      
      // Add basic sitemap properties
      xml += `    <changefreq>${pageKey === '' ? 'daily' : 'weekly'}</changefreq>\n`;
      xml += `    <priority>${pageKey === '' ? '1.0' : '0.8'}</priority>\n`;

      // End URL entry
      xml += '  </url>\n';
    }
  }

  // XML footer
  xml += '</urlset>';

  // Return the XML sitemap with appropriate headers
  return new NextResponse(xml, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600',
    },
  });
}