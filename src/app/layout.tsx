import './globals.css';
import { Inter } from "next/font/google";
import GoogleAnalytics from '@/components/GoogleAnalytics';

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

export const metadata = {
  title: "Týmbox - Docházka online zdarma",
  description: 'Týmbox - jednoduchý online docházkový systém zdarma, rezervační systém a plánování schůzek. Méně chaosu, více času pro vaši firmu.',
  keywords: "docházkový systém online, docházka online zdarma, plánování schůzek online, online rezervační systém, jednoduchý rezervační systém"
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="cs">
      <head />
      <body className={`${inter.variable} font-sans antialiased`}>
        {children}
        <GoogleAnalytics />
      </body>
    </html>
  );
} 