@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --green-primary: #27A844;
  --green-secondary: #218838;
  --green-light: #dcfce7;
  --charcoal: #1f2937;
  --stone: #44403c;
  --bg-light: #fcfcfc;
  --bg-alt: #dbdbdb;
  --accent: #f97316;
  --yellow: #fbbf24;
  --blue: #007BFF;
  --purple: #8b5cf6;
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    /* --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0; */
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply text-slate-800 bg-gray-50;
  }
}

@layer components {
  .container {
    @apply w-full max-w-screen-xl mx-auto px-4 md:px-6;
  }

  .footer-badge-link {
    @apply inline-flex bg-blue text-white font-bold rounded-full text-xs md:text-sm px-4 py-2;
  }

  .footer-badge-link--alt {
    background-color: #f59e42; 
    color: #fff;
    border: none;
  }
  
  .markdown-article {
    @apply max-w-4xl mx-auto px-4;
  }
  
  .markdown-article h1 {
    @apply mb-10 text-center text-5xl font-black tracking-tight leading-none md:text-6xl;
  }
  
  .markdown-article h2 {
    @apply text-2xl md:text-4xl font-black mt-10 leading-none tracking-tight text-charcoal
  }
  
  .markdown-article h3 {
    @apply mx-auto my-4 max-w-3xl text-center text-xl font-bold tracking-tight text-gray-700 md:text-2xl;
  }

  .markdown-article h4 {
    /* @apply my-4 text-lg leading-relaxed; */
    @apply text-md md:text-lg text-gray-700 max-w-2xl mx-auto text-center;
  }
  
  .markdown-article p {
    /* @apply my-4 text-lg leading-relaxed; */
    @apply text-md md:text-lg mt-4 text-gray-700 max-w-2xl mx-auto;
  }
  
  .markdown-article ul, .markdown-article ol {
    @apply my-4 ml-6 text-lg;
  }
  
  .markdown-article li {
    @apply mb-2;
  }
  
  .markdown-article a {
    @apply text-blue hover:underline;
  }
  
  .markdown-article strong {
    @apply font-bold;
  }
  
  .markdown-article em {
    @apply italic;
  }
  
  .markdown-article blockquote {
    @apply pl-4 border-l-4 border-gray-300 italic my-4;
  }
  
  .markdown-article code {
    @apply font-mono bg-gray-100 px-1 py-0.5 rounded text-sm;
  }
  
  .markdown-article pre {
    @apply bg-gray-100 p-4 rounded-lg overflow-x-auto my-4;
  }

  /* Styles for Terms/Privacy Pages */

  .terms {
    padding-left: 1.5em;
    font-size: 14px;
    color: #2a2d34; /* text */
    line-height: 1.5;
  }

  .terms > li {
    margin-bottom: 1em;
  }

  .terms ol {
    list-style-type: decimal;
    padding-left: 1.5em;
    margin-top: 0.5em;
  }

  .terms ol ol {
    list-style-type: lower-alpha;
    padding-left: 1.5em;
    margin-top: 0.5em;
  }

  .terms-effective-date {
    margin: 1em 0;
    font-size: 14px; 
    color: #2a2d34; 
  }

}

@layer text-md {
  .text-md {
    @apply text-base;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

.image-gradient-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(to top left, rgba(203, 232, 255, 0.6), rgba(255, 255, 255, 0.8));
  border-radius: 0.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
