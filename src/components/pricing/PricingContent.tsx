"use client";

import { useTranslations } from 'next-intl';
import { useModal } from '@/context/ModalContext';
import { Link } from '@/i18n/navigation';
export default function PricingContent() {
  const tCenik = useTranslations('CenikPage');
  const t = useTranslations('common');
  const { openOrderModal } = useModal();

  return (
    <div className="min-h-screen bg-gray-50">
      <section className="pt-20 pb-10">
        <div className="container mx-auto px-4 mt-8">
          <div className="max-w-4xl mx-auto text-center">
          <h1 className="mb-4 text-center text-5xl font-black tracking-tight leading-none md:text-6xl">
            <span className="text-charcoal">{tCenik('mainHeading')}</span>
          </h1>
          <h2 className="mx-auto mb-10 max-w-3xl text-center text-xl font-bold tracking-tight text-gray-700 md:text-2xl">
            {tCenik('subtitle')}
          </h2>
          </div>
        </div>
      </section>

      <main className="mx-auto max-w-3xl px-4 pb-12">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 justify-items-center md:items-start">
          {/* Plan Start */}
          <div className="bg-white rounded-xl shadow-md p-8 relative border border-gray-100 transition-shadow hover:shadow-lg w-full max-w-[352px]">
            <h3 className="text-2xl font-bold mb-2 text-green-primary">
              {tCenik('planStart.name')}
            </h3>
            <div className="mb-0">
              <span className="text-2xl font-bold text-charcoal">{tCenik('planStart.price')}</span>
            </div>
            <p className="text-gray-600 mb-6">{tCenik('planStart.period')}</p>
            <ul className="space-y-3 mb-8 text-gray-600">
              <li className="flex items-start">
                <span className="text-green-primary mr-2 mt-1 shrink-0">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5 12L10 17L20 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </span>
                <span className="font-bold">{tCenik('planStart.features.item1')}</span>
              </li>
              <li className="flex items-start">
                <span className="text-green-primary mr-2 mt-1 shrink-0">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5 12L10 17L20 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </span>
                <span>{tCenik('planStart.features.item2')}</span>
              </li>
              <li className="flex items-start">
                <span className="text-green-primary mr-2 mt-1 shrink-0">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5 12L10 17L20 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </span>
                <span>{tCenik('planStart.features.item3')}</span>
              </li>
              <li className="flex items-start">
                <span className="text-green-primary mr-2 mt-1 shrink-0">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5 12L10 17L20 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </span>
                <span>{tCenik('planStart.features.item4')}</span>
              </li>
              <li className="flex items-start">
                <span className="text-green-primary mr-2 mt-1 shrink-0">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5 12L10 17L20 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </span>
                <span>{tCenik('planStart.features.item5')}</span>
              </li>
            </ul>
            <Link
              href="/registrace"
              className="w-full block text-center rounded-full px-6 py-3 text-base font-medium transition-colors shadow-sm bg-green-primary hover:bg-green-secondary text-white"
            >
              {tCenik('planStart.cta')}
            </Link>
          </div>

          {/* Plan Plus */}
          <div className="bg-white rounded-xl shadow-md p-8 relative border border-gray-100 transition-shadow hover:shadow-lg w-full max-w-[352px]">
            <h3 className="text-2xl font-bold mb-2 text-blue">
              {tCenik('planPlus.name')}
            </h3>
            <div className="mb-0">
              <span className="text-2xl font-bold text-charcoal">{tCenik('planPlus.price')}</span>
            </div>
            <p className="text-gray-600 mb-6">{tCenik('planPlus.period')}</p>
            <ul className="space-y-3 mb-8 text-gray-600">
              <li className="flex items-start">
                <span className="text-green-primary mr-2 mt-1 shrink-0">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5 12L10 17L20 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </span>
                <span>{tCenik('planPlus.features.item1')}</span>
              </li>
              <li className="flex items-start">
                <span className="text-green-primary mr-2 mt-1 shrink-0">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5 12L10 17L20 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </span>
                <span className="font-bold">{tCenik('planPlus.features.item2')}</span>
              </li>
              <li className="flex items-start">
                <span className="text-green-primary mr-2 mt-1 shrink-0">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5 12L10 17L20 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </span>
                <span className="font-bold">{tCenik('planPlus.features.item3')}</span>
              </li>
              <li className="flex items-start">
                <span className="text-green-primary mr-2 mt-1 shrink-0">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5 12L10 17L20 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </span>
                <span>{tCenik('planPlus.features.item4')}</span>
              </li>
              <li className="flex items-start">
                <span className="text-green-primary mr-2 mt-1 shrink-0">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5 12L10 17L20 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </span>
                <span>{tCenik('planPlus.features.item5')}</span>
              </li>
              <li className="flex items-start">
                <span className="text-green-primary mr-2 mt-1 shrink-0">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5 12L10 17L20 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </span>
                <span>{tCenik('planPlus.features.item6')}</span>
              </li>
            </ul>
            <button
              onClick={() => openOrderModal('Plus')}
              className="w-full block text-center rounded-full px-6 py-3 text-base font-medium transition-colors shadow-sm bg-blue hover:bg-blue-secondary text-white"
            >
              {tCenik('planPlus.cta')}
            </button>
          </div>
        </div>
      </main>

      <section className="py-10">
        <div className="mx-auto max-w-6xl px-4">

          <div className="text-center mb-16">
            <h2 className="text-2xl md:text-4xl font-black mb-4 leading-none tracking-tight text-charcoal">
              {tCenik('sectionOne.title')}
            </h2>
            <p className="text-md md:text-lg text-gray-700 max-w-2xl mx-auto">
              {tCenik('sectionOne.text')}
            </p>
          </div>

          <div className="text-center mb-16">
            <h2 className="text-2xl md:text-4xl font-black mb-4 leading-none tracking-tight text-charcoal">
              {tCenik('sectionTwo.title')}
            </h2>
            <p className="text-md md:text-lg text-gray-700 max-w-2xl mx-auto">
              {tCenik('sectionTwo.text')}
            </p>
          </div>

          <div className="text-center mb-16">
            <h2 className="text-2xl md:text-4xl font-black mb-4 leading-none tracking-tight text-charcoal">
              {tCenik('sectionThree.title')}
            </h2>
            <p className="text-md md:text-lg text-gray-700 max-w-2xl mx-auto">
              {tCenik('sectionThree.text')}
            </p>
          </div>
          
        </div>
      </section>

      <section className="py-10">
        <div className="mx-auto max-w-3xl px-6 md:px-20">
          <div className="text-center mb-16">
            <h2 className="text-2xl md:text-4xl font-black mb-4 leading-none tracking-tight text-charcoal">
              {tCenik('sectionFAQ.mainTitle')}
            </h2>
          </div>
          <div className="text-start mb-16">
            <h2 className="text-center text-xl md:text-3xl font-black mb-4 leading-none tracking-tight text-charcoal">
              {tCenik('sectionFAQ.q1.title')}
            </h2>
            <p className="text-center text-md md:text-lg text-gray-700 max-w-2xl mx-auto">
              {tCenik('sectionFAQ.q1.text')}
            </p>
          </div>

          <div className="text-start mb-16">
            <h2 className="text-center text-xl md:text-3xl font-black mb-4 leading-none tracking-tight text-charcoal">
              {tCenik('sectionFAQ.q2.title')}
            </h2>
            <p className="text-center text-md md:text-lg text-gray-700 max-w-2xl mx-auto">
              {tCenik('sectionFAQ.q2.text')}
            </p>
          </div>

          <div className="text-start mb-16">
            <h2 className="text-center text-xl md:text-3xl font-black mb-4 leading-none tracking-tight text-charcoal">
              {tCenik('sectionFAQ.q3.title')}
            </h2>
            <p className="text-center text-md md:text-lg text-gray-700 max-w-2xl mx-auto">
              {tCenik('sectionFAQ.q3.textPt1')}
              <span className="font-bold"> {tCenik('sectionFAQ.q3.textPt2Bold')}</span>
              {tCenik('sectionFAQ.q3.textPt3')}
            </p>
          </div>

          <div className="flex justify-center mt-16 mb-8">
            <div className="bg-green-light rounded-2xl shadow-lg p-8 max-w-2xl w-full flex flex-col items-center">
              <h3 className="text-2xl md:text-3xl font-black text-charcoal mb-2 text-center">
                {t('ctaTitle1')}
              </h3>
              <p className="text-md md:text-lg text-gray-700 mb-6 text-center">
                {t('ctaSubtitle1')}
              </p>
              <Link
                href="/registrace"
                className="rounded-full bg-green-primary px-8 py-4 text-lg font-bold text-white shadow-lg hover:bg-green-secondary transition-colors duration-200"
              >
                {t('getStarted1')}
              </Link>
            </div>
          </div>
          
        </div>
      </section>


    </div>
  );
} 