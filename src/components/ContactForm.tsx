"use client";

import { useState, ChangeEvent, FormEvent } from 'react';
import { useTranslations } from 'next-intl';

export default function ContactForm() {
  const t = useTranslations('ContactForm'); // Assuming you will add translations for this
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState('');
  const [status, setStatus] = useState(''); // For success/error messages
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setStatus('');

    if (!name || !email || !message) {
      setStatus(t('errorAllFieldsRequired'));
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name, email, message }),
      });

      if (response.ok) {
        try {
          const result = await response.json();
          if (result.success) {
            setStatus(t('successMessage'));
            setName('');
            setEmail('');
            setMessage('');
          } else {
            setStatus(result.error || t('errorMessage'));
          }
        } catch (jsonError) {
          setStatus(t('successMessage'));
          setName('');
          setEmail('');
          setMessage('');
        }
      } else {
        try {
          const result = await response.json();
          setStatus(result.error || t('errorMessage'));
        } catch (jsonError) {
          setStatus(t('errorMessage'));
        }
      }
    } catch (error) {
      console.error('Contact form submission error:', error);
      setStatus(t('errorMessage'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 max-w-lg mx-auto bg-white p-8 rounded-lg shadow-md border border-gray-100" data-testid="contact-form">
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-gray-700">
          {t('labelName')}
        </label>
        <input
          type="text"
          name="name"
          id="name"
          value={name}
          onChange={(e: ChangeEvent<HTMLInputElement>) => setName(e.target.value)}
          required
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-primary focus:border-green-primary sm:text-sm"
          data-testid="name-input"
        />
      </div>
      <div>
        <label htmlFor="email" className="block text-sm font-medium text-gray-700">
          {t('labelEmail')}
        </label>
        <input
          type="email"
          name="email"
          id="email"
          value={email}
          onChange={(e: ChangeEvent<HTMLInputElement>) => setEmail(e.target.value)}
          required
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-primary focus:border-green-primary sm:text-sm"
          data-testid="email-input"
        />
      </div>
      <div>
        <label htmlFor="message" className="block text-sm font-medium text-gray-700">
          {t('labelMessage')}
        </label>
        <textarea
          name="message"
          id="message"
          rows={4}
          value={message}
          onChange={(e: ChangeEvent<HTMLTextAreaElement>) => setMessage(e.target.value)}
          required
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-primary focus:border-green-primary sm:text-sm"
          data-testid="message-input"
        />
      </div>
      <div>
        <button
          type="submit"
          disabled={isLoading}
          className="w-full flex justify-center py-3 px-4 border border-transparent rounded-full shadow-sm text-base font-medium text-white bg-green-primary hover:bg-green-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-primary disabled:opacity-50"
          data-testid="submit-button"
        >
          {isLoading ? t('buttonSending') : t('buttonSend')}
        </button>
      </div>
      {status && (
        <p className={`text-sm text-center ${status.includes(t('errorMessage')) || status.includes(t('errorAllFieldsRequired')) ? 'text-red-500' : 'text-green-600'}`} data-testid="form-status">
          {status}
        </p>
      )}
    </form>
  );
} 