'use client';

import Script from 'next/script';
import { useEffect, useState } from 'react';

const GA_TRACKING_ID = 'G-HWE817PRNM';

// TypeScript types for requestIdleCallback
type IdleDeadline = {
  timeRemaining: () => number;
  didTimeout: boolean;
};

type IdleCallbackOptions = {
  timeout?: number;
};

export default function GoogleAnalytics() {
  const [shouldLoad, setShouldLoad] = useState(false);

  useEffect(() => {
    const loadGA = () => setShouldLoad(true);
    
    // Strategy 1: Load on first user interaction
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    const handleInteraction = () => {
      loadGA();
      cleanup();
    };

    // Strategy 2: Load when page is idle (using requestIdleCallback)
    const handleIdle = () => {
      loadGA();
      cleanup();
    };

    // Strategy 3: Load after critical content is visible (using Intersection Observer)
    const observeContent = () => {
      const mainContent = document.querySelector('main, [role="main"], h1');
      if (mainContent) {
        const observer = new IntersectionObserver((entries) => {
          if (entries[0].isIntersecting) {
            // Wait a bit more after main content is visible
            setTimeout(() => {
              loadGA();
              cleanup();
            }, 1000);
            observer.disconnect();
          }
        });
        observer.observe(mainContent);
        return () => observer.disconnect();
      }
      return () => {};
    };

    const cleanup = () => {
      events.forEach(event => {
        document.removeEventListener(event, handleInteraction, true);
      });
      clearTimeout(fallbackTimer);
      clearTimeout(idleTimer);
    };

    // Add event listeners for user interactions
    events.forEach(event => {
      document.addEventListener(event, handleInteraction, true);
    });

    // Use requestIdleCallback if available, otherwise setTimeout
    const idleTimer = 'requestIdleCallback' in window 
      ? (window as any).requestIdleCallback(handleIdle, { timeout: 2000 })
      : setTimeout(handleIdle, 2000);

    // Observe main content
    const disconnectObserver = observeContent();

    // Fallback: load after 4 seconds if nothing else triggered
    const fallbackTimer = setTimeout(() => {
      loadGA();
      cleanup();
    }, 4000);

    return () => {
      cleanup();
      disconnectObserver();
    };
  }, []);

  if (!shouldLoad) {
    return null;
  }

  return (
    <>
      <Script
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`}
        strategy="afterInteractive"
      />
      <Script id="google-analytics" strategy="afterInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '${GA_TRACKING_ID}', {
            page_title: document.title,
            page_location: window.location.href,
          });
        `}
      </Script>
    </>
  );
} 