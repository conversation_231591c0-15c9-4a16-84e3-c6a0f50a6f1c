"use client";
import Image from "next/image";
import { useState, useEffect, useRef } from 'react';
import { Link, usePathname } from '@/i18n/navigation';
import { useTranslations } from 'next-intl';
import { useLocale } from 'next-intl';

interface NavItem {
  key: string;
}

const navItems: NavItem[] = [
  { key: 'attendance' },
  { key: 'bookings' },
  { key: 'meetings' },
  { key: 'pricing' },
];

export default function Header() {
  const t = useTranslations('header');
  const pathT = useTranslations('paths');
  const locale = useLocale() as 'cs' | 'sk';
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isMobileMenuOpen &&
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsMobileMenuOpen(false);
      }
    };

    if (isMobileMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMobileMenuOpen]);

  const loginUrls: Record<string, string> = {
    cs: 'https://app.tymbox.cz/cs/users/sign_in',
    sk: 'https://app.tymbox.cz/sk/users/sign_in',
    // Add more locales and URLs here
  };
  const loginUrl = loginUrls[locale] || loginUrls['cs'];

  return (
    <>
      {/* Gray background overlay */}
      {isMobileMenuOpen && (
        <div className="fixed inset-0 bg-gray-800/50 transition-opacity z-40 md:hidden" onClick={() => setIsMobileMenuOpen(false)} />
      )}

      <header className="fixed top-0 left-0 right-0 z-50 mx-auto max-w-7xl px-4 py-4">
        <div className="relative flex items-center justify-between rounded-full bg-white p-2 shadow-lg px-8">
          <div className="flex items-center gap-8">
            <Link href="/" className="flex items-center" data-testid="logo-link">
              <Image src="/logo.png" alt="Týmbox Logo" width={80} height={28} priority />
            </Link>
          </div>
            <nav className="hidden md:block" data-testid="main-navigation">
              <ul className="flex space-x-6">
                {navItems.map((item) => (
                  <li key={item.key}>
                    <Link
                      href={pathT(item.key)}
                      className="font-bold text-gray-700 hover:text-gray-900"
                      data-testid={`nav-${item.key}`}
                    >
                      {t(item.key)}
                    </Link>
                  </li>
                ))}
              </ul>
            </nav>
          <div className="flex items-center gap-3">
            <Link
              href={loginUrl}
              className="hidden md:flex rounded-full bg-green-primary px-5 py-2 text-sm font-medium text-white"
              data-testid="login-button"
            >
              {t('login')}
            </Link>
            <button
              className="md:hidden text-md font-bold text-gray-700"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              ref={buttonRef}
              data-testid="mobile-menu-toggle"
            >
              {t('menu')}
            </button>
          </div>

          {isMobileMenuOpen && (
            <div
              ref={menuRef}
              className="absolute z-50 top-full w-full left-0 mt-2 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 md:hidden"
              data-testid="mobile-menu"
            >
              <div className="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
                {navItems.map((item) => (
                  <Link
                    key={item.key}
                    href={pathT(item.key)}
                    className="block px-4 py-4 text-md font-semibold text-gray-700 hover:bg-gray-100 hover:text-gray-900 text-center"
                    role="menuitem"
                    onClick={() => setIsMobileMenuOpen(false)}
                    data-testid={`mobile-nav-${item.key}`}
                  >
                    {t(item.key)}
                  </Link>
                ))}
                <Link
                  href={loginUrl}
                  className="block px-4 py-4 text-md font-semibold text-green-primary hover:bg-gray-100 text-center"
                  role="menuitem"
                  onClick={() => setIsMobileMenuOpen(false)}
                  data-testid="mobile-login-button"
                >
                  {t('login')}
                </Link>
              </div>
            </div>
          )}
        </div>
      </header>
    </>
  );
}