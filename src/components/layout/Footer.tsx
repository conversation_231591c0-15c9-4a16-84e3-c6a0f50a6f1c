"use client";

import { useTranslations } from 'next-intl';
import { Link } from '@/i18n/navigation';
import { useLocale } from 'next-intl';

interface FooterLink {
  key: string;
}

export default function Footer() {
  const t = useTranslations('footer');
  const pathT = useTranslations('paths');
  const currentYear = new Date().getFullYear();
  const locale = useLocale();
  
  const footerLinks: FooterLink[] = [
    { key: 'bookings' },
    { key: 'meetings' },
    { key: 'attendance' },
    { key: 'why_free' },
    { key: 'no_install' },
    { key: 'for_small' },
    { key: 'simple' },
    { key: 'pricing' },
    { key: 'policies' },
    { key: 'privacy' },
    { key: 'contact' },
  ];
  
  return (
    <footer className="mx-auto max-w-6xl px-4 py-4 pt-12 pb-6">
      <div className="container">
        <ul className="flex flex-wrap align-center justify-center gap-2 mb-8">
          {footerLinks.map((link) => (
            <li key={link.key}>
              <Link href={pathT(link.key)} className="footer-badge-link">
                {t(link.key)}
              </Link>
            </li>
          ))}
          <li>
            <a
              href="/cs"
              className={locale === 'cs' ? 'footer-badge-link' : 'footer-badge-link footer-badge-link--alt'}
            >
              Česky
            </a>
          </li>
          <li>
            <a
              href="/sk"
              className={locale === 'sk' ? 'footer-badge-link' : 'footer-badge-link footer-badge-link--alt'}
            >
              Slovensky
            </a>
          </li>
          <li>
            <a
              href="/en"
              className={locale === 'en' ? 'footer-badge-link' : 'footer-badge-link footer-badge-link--alt'}
            >
              English
            </a>
          </li>
        </ul>

        <div className="pt-6">
          <div className="flex flex-col md:flex-row align-center justify-center items-center">
            <p className="text-sm text-center mb-1 md:mb-0">
              &copy; {currentYear} Týmbox | {t('tagline')}
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}