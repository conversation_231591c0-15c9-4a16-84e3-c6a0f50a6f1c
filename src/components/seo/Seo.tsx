"use client";

import { Metadata } from 'next';

interface SeoProps {
  title?: string;
  description?: string;
  canonical?: string;
  ogType?: 'website' | 'article';
  ogImage?: string;
  structuredData?: object;
}

export default function Seo({
  title = 'Týmbox - Jednoduchý docházkový systém online zdarma | Online rezervace',
  description = 'Týmbox - jednoduchý online docházkový systém zdarma, rezervační systém a plánování schůzek. Méně chaosu, více času pro vaši firmu.',
  canonical,
  ogType = 'website',
  ogImage = '/images/og-image.png',
  structuredData,
}: SeoProps) {
  const site = {
    name: 'Týmbox',
    url: 'https://týmbox.cz',
  };
  
  const fullTitle = title.includes(site.name) ? title : `${title} | ${site.name}`;
  
  return (
    <>
      {structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
      )}
    </>
  );
}

export function generateMetadata({
  title = 'Týmbox - Jednoduchý docházkový systém online zdarma | Online rezervace',
  description = 'Týmbox - jednoduchý online docházkový systém zdarma, rezervační systém a plánování schůzek. Méně chaosu, více času pro vaši firmu.',
  canonical,
  ogType = 'website',
  ogImage = '/images/og-image.png',
}: SeoProps): Metadata {
  const site = {
    name: 'Týmbox',
    url: 'https://tymbox.cz',
  };
  
  const fullTitle = title.includes(site.name) ? title : `${title} | ${site.name}`;
  
  return {
    title: fullTitle,
    description,
    openGraph: {
      title: fullTitle,
      description,
      type: ogType,
      siteName: site.name,
      url: canonical ? `${site.url}${canonical}` : undefined,
      images: [
        {
          url: `${site.url}${ogImage}`,
          alt: fullTitle,
        },
      ],
      locale: 'cs_CZ',
    },
    twitter: {
      card: 'summary_large_image',
      title: fullTitle,
      description,
      images: [`${site.url}${ogImage}`],
    },
    alternates: {
      canonical: canonical ? `${site.url}${canonical}` : undefined,
    },
  };
} 