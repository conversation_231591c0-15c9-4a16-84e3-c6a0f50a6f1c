/**
 * Get the current locale in server components
 * This is needed for functions that require explicit locale like getLocalizedMarkdown
 * In single-domain architecture, this defaults to Czech
 * @returns {string} The detected locale
 */
export async function getServerLocale() {
  // In single-domain architecture, the locale should be passed as a parameter
  // This is a fallback function that defaults to Czech
  return 'cs';
}