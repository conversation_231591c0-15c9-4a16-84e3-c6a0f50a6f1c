import { siteConfig } from '@/config/site';
import { BASE_URL, getLocaleConfig, getAllLocales } from '@/config/domains';
import { pathMappings, getSeoPath } from '@/config/paths';
import { getTranslations } from 'next-intl/server';

// Note: Localized paths now imported from centralized config

/**
 * Generate SEO metadata with proper canonical URLs and cross-domain hreflang tags
 * Updated to work with multi-domain single deployment
 * 
 * @param {Object} options - Options for metadata generation
 * @param {string} [options.locale] - Current locale (optional, auto-detected from domain if not provided)
 * @param {string} options.pagePath - Path of the current page (without locale prefix)
 * @param {string} options.namespace - Translation namespace to use for title/description
 * @param {string} options.defaultTitle - Default title if translation fails
 * @param {string} options.defaultDescription - Default description if translation fails
 * @returns {Object} Next.js metadata object
 */
export async function generatePageMetadata({
  locale = 'cs',
  pagePath = '',
  namespace = 'common',
  defaultTitle,
  defaultDescription
}) {
  // Use the locale parameter directly (provided by next-intl)
  const actualLocale = locale || 'cs';
  
  // Get locale-specific configuration
  const localeConfig = getLocaleConfig(actualLocale);
  
  // Ensure pagePath starts with / if not empty
  if (pagePath && !pagePath.startsWith('/')) {
    pagePath = `/${pagePath}`;
  }
  
  // Default titles and descriptions based on locale
  const defaults = {
    cs: {
      title: defaultTitle || 'Týmbox - Jednoduchý docházkový systém online zdarma | Online rezervace',
      description: defaultDescription || 'Týmbox - jednoduchý online docházkový systém zdarma, rezervační systém a plánování schůzek. Méně chaosu, více času pro vaši firmu.',
      siteName: 'Týmbox'
    },
    sk: {
      title: defaultTitle || 'Teambox - Jednoduchý dochádzkovový systém online zadarmo | Online rezervácie',
      description: defaultDescription || 'Teambox - jednoduchý online dochádzkovový systém zadarmo, rezervačný systém a plánovanie stretnutí. Menej chaosu, viac času pre vašu firmu.',
      siteName: 'Teambox'
    },
    en: {
      title: defaultTitle || 'Tymbox - Simple Online Attendance System Free | Online Booking',
      description: defaultDescription || 'Tymbox - simple online attendance system for free, booking system and meeting planning. Less chaos, more time for your business.',
      siteName: 'Tymbox'
    }
  };
  
  const localeDefaults = defaults[actualLocale] || defaults.cs;
  
  // Try to get translations
  let title = localeDefaults.title;
  let description = localeDefaults.description;
  
  try {
    const t = await getTranslations({ locale: actualLocale, namespace });
    title = t('metaTitle') || t('title') || localeDefaults.title;
    description = t('metaDescription') || localeDefaults.description;
  } catch (error) {
    console.error(`Missing translations for locale ${actualLocale} in ${namespace} namespace:`, error);
  }

  // Find the page key from the pagePath
  const pageKey = Object.keys(pathMappings).find(key => 
    pathMappings[key][actualLocale] === pagePath
  ) || pagePath.replace('/', '');
  
  // Get canonical path for current locale with locale prefix
  const localizedPath = getSeoPath(pageKey, actualLocale);
  
  // Construct canonical URL with locale prefix
  const canonicalUrl = `${BASE_URL}/${actualLocale}${localizedPath}`;
  
  // Create language alternates for all locales
  const languageAlternates = {};
  const allLocales = getAllLocales();
  
  // Generate hreflang for each locale
  allLocales.forEach(localeCode => {
    const localeConf = getLocaleConfig(localeCode);
    const alternatePath = getSeoPath(pageKey, localeCode);
    languageAlternates[localeConf.language] = `${BASE_URL}/${localeCode}${alternatePath}`;
  });
  
  const fullTitle = title.includes(localeDefaults.siteName) ? title : `${title} | ${localeDefaults.siteName}`;
  
  return {
    title: fullTitle,
    description,
    alternates: {
      canonical: canonicalUrl,
      languages: languageAlternates,
    },
    metadataBase: new URL(BASE_URL),
    openGraph: {
      title: fullTitle,
      description,
      url: canonicalUrl,
      siteName: localeDefaults.siteName,
      locale: localeConfig.language,
      type: 'website',
      images: [
        {
          url: `${BASE_URL}/images/og-image.png`,
          alt: fullTitle,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: fullTitle,
      description,
      images: [`${BASE_URL}/images/og-image.png`],
    },
  };
}

/**
 * Legacy function for backward compatibility
 * Redirects to the new generatePageMetadata function
 */
export async function generateHreflangMetadata(options) {
  return generatePageMetadata(options);
}