import { BASE_URL, getLocaleConfig } from '@/config/domains';

// ABOUTME: Legacy domain detection utilities - kept for backward compatibility
// ABOUTME: In single-domain architecture, these functions are mostly unused

/**
 * Legacy function for backward compatibility
 * In single-domain architecture, locale is determined by URL path, not domain
 * @param {string} locale - The locale to get config for
 * @returns {Object} Locale configuration
 */
export const getLocaleConfigForDomain = (locale = 'cs') => {
  return getLocaleConfig(locale);
};

/**
 * Check if we're in development mode
 * @param {string} hostname - The hostname to check
 * @returns {boolean} True if in development mode
 */
export const isDevelopment = (hostname) => {
  return hostname.includes('localhost') || 
         hostname.includes('127.0.0.1') ||
         hostname.includes('netlify.app') ||
         hostname.includes('netlify.live');
};

/**
 * Get the base URL for the application
 * @returns {string} The base URL
 */
export const getBaseUrl = () => {
  return BASE_URL;
};