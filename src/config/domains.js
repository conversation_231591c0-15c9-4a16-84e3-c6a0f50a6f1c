// ABOUTME: Single-domain configuration for internationalization with locale prefixes
// ABOUTME: Provides locale-specific settings for the tymbox.cz domain with /cs, /sk, /en paths

// A single source of truth for the application's base URL
// Use this to construct absolute URLs for hreflang, sitemaps, etc.
export const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'https://www.tymbox.cz';

const localeConfigs = {
  'cs': {
    locale: 'cs',
    name: 'Týmbox CZ',
    registrationUrl: 'https://app.tymbox.cz/cs/users/sign_up',
    country: 'CZ',
    language: 'cs-CZ', // Used for the `lang` attribute in <html>
  },
  'sk': {
    locale: 'sk',
    name: 'Teambox SK',
    registrationUrl: 'https://app.tymbox.cz/sk/users/sign_up',
    country: 'SK',
    language: 'sk-SK',
  },
  'en': {
    locale: 'en',
    name: 'Tymbox EN',
    registrationUrl: 'https://app.tymbox.cz/en/users/sign_up',
    country: 'US',
    language: 'en-US',
  }
};

/**
 * Retrieves the configuration for a specific locale.
 * @param {string} locale - The locale code (e.g., 'cs', 'sk').
 * @returns {object} The configuration object for the given locale.
 */
export const getLocaleConfig = (locale) => {
  return localeConfigs[locale] || localeConfigs['cs'];
};

/**
 * Returns an array of all supported locale codes.
 * @returns {string[]}
 */
export const getAllLocales = () => {
  return Object.keys(localeConfigs);
}; 