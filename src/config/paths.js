// ABOUTME: Centralized URL path mappings for all locales to ensure SEO consistency
// ABOUTME: Single source of truth for sitemap generation, rewrites, and canonical URLs

/**
 * Central path mappings for all pages across all locales
 * Key: Internal page identifier (neutral English key)
 * Value: Object with locale-specific SEO-friendly paths
 */
export const pathMappings = {
  'dochazka': {
    cs: '/online-dochazka',
    sk: '/online-dochadzka',
    en: '/attendance'
  },
  'rezervace': {
    cs: '/online-rezervace',
    sk: '/online-rezervacie',
    en: '/bookings'
  },
  'schuzky': {
    cs: '/planovac-schuzek',
    sk: '/planovac-stretnuti',
    en: '/meetings'
  },
  'cenik': {
    cs: '/tymbox-plus',
    sk: '/tymbox-plus',
    en: '/pricing'
  },
  'no_install': {
    cs: '/mobilni-dochazka-bez-instalace',
    sk: '/mobilna-dochadzka-bez-instalacie',
    en: '/no-install'
  },
  'why_free': {
    cs: '/online-dochazka-zdarma',
    sk: '/online-dochadzka-zadarmo',
    en: '/free-attendance'
  },
  'for_small': {
    cs: '/pro-male-firmy',
    sk: '/pre-male-firmy',
    en: '/for-small-business'
  },
  'simple': {
    cs: '/dochazka-bez-skoleni',
    sk: '/dochadzka-bez-skoleni',
    en: '/simple-attendance'
  },
  'faq': {
    cs: '/caste-dotazy',
    sk: '/caste-otazky',
    en: '/faq'
  },
  'policies': {
    cs: '/podminky-sluzby',
    sk: '/podmienky-sluzby',
    en: '/terms'
  },
  'privacy': {
    cs: '/ochrana-dat',
    sk: '/ochrana-dat',
    en: '/privacy'
  },
  'kontakt': {
    cs: '/kontakt',
    sk: '/kontakt',
    en: '/contact'
  }
};

/**
 * Generate Next.js rewrite rules from the path mappings
 * Maps SEO-friendly URLs to internal page directories
 * @returns {Array} Array of rewrite objects for next.config.js
 */
export const generateRewrites = () => {
  const rewrites = [];
  
  for (const [pageKey, locales] of Object.entries(pathMappings)) {
    for (const [locale, path] of Object.entries(locales)) {
      rewrites.push({
        source: `/${locale}${path}`,
        destination: `/${locale}/${pageKey}`,
      });
    }
  }
  
  return rewrites;
};

/**
 * Get the SEO-friendly path for a given page and locale
 * @param {string} pageKey - Internal page identifier
 * @param {string} locale - Target locale
 * @returns {string} SEO-friendly path or fallback
 */
export const getSeoPath = (pageKey, locale) => {
  return pathMappings[pageKey]?.[locale] || `/${pageKey}`;
};

/**
 * Get the internal page key from a SEO-friendly path
 * @param {string} seoPath - SEO-friendly path
 * @param {string} locale - Current locale
 * @returns {string} Internal page key or original path
 */
export const getPageKey = (seoPath, locale) => {
  for (const [pageKey, locales] of Object.entries(pathMappings)) {
    if (locales[locale] === seoPath) {
      return pageKey;
    }
  }
  return seoPath.replace('/', '');
};

/**
 * Get all page keys for sitemap generation
 * @returns {Array} Array of all page keys including homepage
 */
export const getAllPageKeys = () => {
  return ['', ...Object.keys(pathMappings)];
};

/**
 * Generate alternate language links for a given page
 * @param {string} pageKey - Internal page identifier
 * @param {string} baseUrl - Base URL for the site
 * @param {Array} locales - Array of supported locales
 * @returns {Array} Array of alternate language objects
 */
export const generateAlternateLanguages = (pageKey, baseUrl, locales) => {
  const alternates = [];
  
  locales.forEach(locale => {
    const path = pageKey === '' ? '' : getSeoPath(pageKey, locale);
    alternates.push({
      locale,
      href: `${baseUrl}/${locale}${path}`
    });
  });
  
  return alternates;
};