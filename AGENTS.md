# Repository Guidelines

## Project Structure & Module Organization
- src/app: Next.js routes (locale-prefixed via `src/i18n`), API, metadata.
- src/components: Reusable React components (PascalCase, e.g., `Header.tsx`).
- src/config: Site config, SEO path mappings (`paths.js`), locales, domains.
- src/lib, src/utils, src/context: Helpers and shared logic.
- public: Static assets (images, icons, sitemap output).
- tests: `unit/`, `e2e/`, and `utils/` helpers; see `docs/testing`.
- docs: Architecture, locales, and testing notes.

## Build, Test, and Development Commands
- `npm run dev`: Start Next.js dev server at `localhost:3000`.
- `npm run build`: Production build.
- `npm run build:prod`: Build + generate sitemap (`next-sitemap`).
- `npm start`: Run the production server.
- `npm run lint`: ESLint (Next core-web-vitals + TypeScript).
- `npm test`: Unit tests (Vitest).
- `npm run test:e2e`: E2E tests (Playwright). First time: `npx playwright install`.
- Optional dev helper: `./dev-server.sh {start|stop|status|logs|tail}`.

## Coding Style & Naming Conventions
- Language: TypeScript + React functional components.
- Indentation: 2 spaces; keep lines short and clear.
- Filenames: Components PascalCase (`OrderForm.tsx`); routes/config lower- or kebab-case.
- Imports: Use `@` alias for `src` (see `vitest.config.ts`).
- Linting: Fix issues reported by `npm run lint` before PR.
- Styling: TailwindCSS; prefer utility classes close to components.

## Testing Guidelines
- Frameworks: Vitest (+ Testing Library) for unit; Playwright for E2E.
- Locations: Place unit tests under `tests/unit` as `*.test.{js,jsx,ts,tsx}`; E2E in `tests/e2e`.
- Running: `npm test` (unit), `npm run test:e2e` (spawns dev server per `playwright.config.ts`).
- Scope: Focus on routing, i18n, SEO paths, and critical UX. See `docs/testing` for examples.

## Commit & Pull Request Guidelines
- Commits: Use short, descriptive, imperative subjects. Examples: "Fix Slovak registration link", "Refactor sitemap generation". Add a brief body when useful.
- PRs: Include summary, linked issues, what/why, screenshots for UI, and testing notes (unit/E2E ran, results). Keep diffs focused.

## Security & Configuration Tips
- Environment: `NEXT_PUBLIC_SITE_URL` used in `src/config/site.js`.
- i18n: Update routing in `src/i18n` and locale paths in `src/config/paths.js` to keep rewrites, sitemaps, and canonical URLs consistent.
