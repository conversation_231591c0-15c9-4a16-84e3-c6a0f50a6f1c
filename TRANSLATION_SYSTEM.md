# Translation System Documentation

## Overview

This project uses a **runtime domain-based translation system** where the language is determined by the domain accessing the site. A single deployment serves all domains with their respective languages.

## Key Principles

1. **Single Build, Multiple Domains**: One Next.js build serves all domains
2. **Runtime Locale Detection**: Language determined by domain at request time
3. **No Locale in URLs**: Clean URLs without language prefixes
4. **Automatic Language Switching**: Access different domain = different language

## Architecture Components

### 1. Domain Configuration (`src/config/domains.js`)

Central configuration mapping domains to locales:

```javascript
const domainConfigs = {
  'tymbox.cz': {
    locale: 'cs',
    name: 'Týmbox CZ',
    registrationUrl: 'https://app.tymbox.cz/cs/users/sign_up',
    // ... other config
  },
  'teambox.sk': {
    locale: 'sk',
    name: 'Teambox SK',
    registrationUrl: 'https://app.tymbox.cz/sk/users/sign_up',
    // ... other config
  }
};
```

### 2. Domain Detection (`src/lib/domain-detector.js`)

Utilities for extracting and mapping domains:
- `detectHostname(request)` - Extracts hostname from headers
- `detectDomain(request)` - Returns domain configuration
- `detectLocaleFromDomain(request)` - Returns locale for domain

### 3. Middleware (`middleware.js`)

Intercepts all requests to:
1. Detect incoming domain
2. Map domain to locale
3. Pass locale to next-intl
4. Set NEXT_LOCALE cookie
5. Handle domain-specific redirects

### 4. Request Configuration (`src/i18n/request.js`)

Configures next-intl with detected locale:
```javascript
export default getRequestConfig(async () => {
  const detectedLocale = detectLocaleFromDomain(request);
  return {
    locale: detectedLocale,
    messages: (await import(`../../messages/${detectedLocale}.json`)).default
  };
});
```

### 5. Translation Files (`messages/`)

JSON files containing translations:
- `messages/cs.json` - Czech translations
- `messages/sk.json` - Slovak translations
- `messages/en.json` - English translations (if needed)

## How It Works

### Request Flow

1. **User visits teambox.sk**
2. **Middleware detects domain** → maps to Slovak locale
3. **Request config loads Slovak messages**
4. **Pages render with Slovak content**
5. **Metadata generates Slovak SEO tags**

### In Components

#### Client Components
```tsx
'use client';
import {useTranslations} from 'next-intl';

export default function MyComponent() {
  const t = useTranslations('HomePage');
  return <h1>{t('title')}</h1>;
}
```

#### Server Components
```tsx
import {getTranslations} from 'next-intl/server';

export default async function MyPage() {
  const t = await getTranslations('HomePage');
  return <h1>{t('title')}</h1>;
}
```

## Local Development

### Setup
1. Add local domains to `/etc/hosts`:
   ```
   127.0.0.1 local.tymbox.cz
   127.0.0.1 local.teambox.sk
   ```

2. Start dev server:
   ```bash
   npm run dev
   ```

3. Access different languages:
   - Czech: http://local.tymbox.cz:3000
   - Slovak: http://local.teambox.sk:3000

### Testing Tips
- Use browser incognito mode to avoid cookie caching
- Check HTML `lang` attribute for correct locale
- Verify translations in page content
- Test registration redirects for each domain

## Production Deployment

### Netlify Configuration
1. Single site deployment
2. Add domain aliases:
   - Primary: tymbox.cz
   - Alias: teambox.sk
3. Environment variables not needed for locale

### Vercel/Other Platforms
- Ensure host headers are forwarded
- Configure domain aliases
- No special build configuration needed

## Common Patterns

### Links Between Pages
```tsx
import {Link} from '@/i18n/navigation';

// Always use relative paths
<Link href="/kontakt">Contact</Link>
// NOT: <Link href="/cs/kontakt">
```

### Dynamic Paths
```tsx
// Paths automatically adjust per locale
const paths = {
  cs: '/online-dochazka',
  sk: '/online-dochadzka'
};
```

### Metadata Generation
```tsx
export async function generateMetadata() {
  // Don't pass locale - it's detected automatically
  return generatePageMetadata({
    pagePath: '/about',
    namespace: 'AboutPage'
  });
}

// Alternative for custom metadata:
export async function generateMetadata() {
  const t = await getTranslations('PageNamespace');
  
  let title = "Fallback Title";
  try {
    title = t('metaTitle');
  } catch (error) {
    console.error('Missing metadata translations for PageNamespace:', error);
  }
  
  return { title };
}
```

### URL Rewrites
SEO-friendly URLs are handled by `next.config.js` rewrites:
```javascript
// Both Czech and Slovak URLs must be defined
{
  source: '/online-dochazka',     // Czech URL
  destination: '/dochazka',       // Actual page
},
{
  source: '/online-dochadzka',    // Slovak URL  
  destination: '/dochazka',       // Same page
}
```

## Do's and Don'ts

### ✅ DO
- Use domain detection for locale
- Keep translations in `messages/` files
- Use next-intl hooks for translations
- Test with local domains
- Let middleware handle locale detection

### ❌ DON'T
- Hardcode locale in pages
- Use `routing.defaultLocale` in components
- Call `setRequestLocale()` in pages
- Pass locale as props
- Use environment variables for locale
- Use `{locale, namespace}` syntax in getTranslations()
- Reference undefined `locale` variable in generateMetadata()
- Use `canonical: `/${locale}/page`` in metadata

## Troubleshooting

### "locale is not defined" Error
**Most Common Issue**: Server components referencing undefined locale variable

✅ **Fix**: Use `getTranslations('namespace')` without locale parameter
❌ **Don't**: Use `getTranslations({locale, namespace: 'PageName'})`

Examples:
```tsx
// ❌ WRONG - Will cause "locale is not defined"
const t = await getTranslations({locale, namespace: 'HomePage'});
console.error(`Missing translations in locale ${locale}:`, error);
canonical: `/${locale}/page`

// ✅ CORRECT  
const t = await getTranslations('HomePage');
console.error('Missing translations for HomePage:', error);
// Don't use canonical with locale in metadata
```

### URLs Returning 404 or 500 Errors
1. **Check URL rewrites** in `next.config.js` - both Czech and Slovak variants needed
2. **Server restart required** after modifying next.config.js rewrites
3. **Check target pages** for undefined locale variables
4. **Verify sitemap URLs** match rewrite rules in `/api/sitemap/route.js`

### Wrong Language Displayed
1. Check domain detection logs in console
2. Verify `/etc/hosts` entries
3. Clear cookies and cache
4. Check if page has hardcoded locale

### Translation Keys Missing
1. Check namespace in useTranslations()
2. Verify key exists in messages JSON
3. Check for typos in translation keys

### Domain Not Recognized
1. Add domain to `domainConfigs`
2. Check hostname format (with/without port)
3. Verify header forwarding in production

## Adding New Languages

1. Add domain config:
   ```javascript
   'tymbox.at': {
     locale: 'de',
     name: 'Týmbox AT',
     // ...
   }
   ```

2. Create translation file:
   ```
   messages/de.json
   ```

3. Add locale to routing:
   ```javascript
   locales: ['cs', 'sk', 'en', 'de']
   ```

4. Configure domain in hosting

## Migration Notes

This system replaced the previous multi-build approach where separate builds were created for each domain. The new approach:
- Reduces build complexity
- Enables easier maintenance
- Supports unlimited domains
- Provides better development experience

## Related Files

- `src/config/domains.js` - Domain configuration
- `src/lib/domain-detector.js` - Detection utilities
- `middleware.js` - Request interception
- `src/i18n/request.js` - Locale configuration
- `src/i18n/routing.js` - Routing setup
- `messages/*.json` - Translation files