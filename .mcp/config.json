{"mcpServers": {"shell": {"command": "npx", "args": ["-y", "mcp-shell"]}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "./src", "./public", "./messages", "./i18n"]}, "git": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-git"]}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"], "env": {"PUPPETEER_HEADLESS": "true"}}, "search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-search"]}, "postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "postgresql://user:password@localhost:5432/tymbox"], "disabled": true}}}