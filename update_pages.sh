#!/bin/bash

# Find all page.tsx files except the main one we already updated
find src/app -name "page.tsx" -not -path "src/app/page.tsx" -not -path "src/app/dochazka/page.tsx" | while read file; do
    echo "Updating $file..."
    
    # Replace the generateMetadata function signature
    sed -i 's/export async function generateMetadata({params: {locale}}: {params: {locale: string}})/export async function generateMetadata()/g' "$file"
    
    # Replace the component function signature
    sed -i 's/export default function \([^(]*\)({params: {locale}}: {params: {locale: string}})/export default function \1()/g' "$file"
    
    # Add locale variable at the beginning of generateMetadata
    sed -i '/export async function generateMetadata() {/a\  const locale = process.env.NEXT_PUBLIC_DEFAULT_LOCALE || '\''cs'\'';' "$file"
    
    # Add locale variable at the beginning of component function
    sed -i '/export default function [^(]*() {/a\  const locale = process.env.NEXT_PUBLIC_DEFAULT_LOCALE || '\''cs'\'';' "$file"
    
    # Update setRequestLocale calls to use the locale variable
    sed -i 's/setRequestLocale(locale);/setRequestLocale(locale);/g' "$file"
    
    echo "Updated $file"
done

echo "All pages updated!" 