#!/bin/bash

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$SCRIPT_DIR"

case "$1" in
  start)
    echo "Starting development server..."
    node scripts/server-manager.js start
    ;;
  stop)
    echo "Stopping development server..."
    node scripts/server-manager.js stop
    ;;
  logs)
    node scripts/server-manager.js logs ${2:-50}
    ;;
  tail)
    node scripts/server-manager.js tail
    ;;
  restart)
    echo "Restarting development server..."
    node scripts/server-manager.js stop
    sleep 2
    node scripts/server-manager.js start
    ;;
  status)
    if [ -f "server.pid" ]; then
      PID=$(cat server.pid)
      if ps -p $PID > /dev/null 2>&1; then
        echo "Server is running (PID: $PID)"
        echo "Recent logs:"
        node scripts/server-manager.js logs 10
      else
        echo "Server PID file exists but process is not running"
        rm -f server.pid
      fi
    else
      echo "Server is not running"
    fi
    ;;
  *)
    echo "Usage: $0 {start|stop|restart|status|logs [n]|tail}"
    echo "  start   - Start the development server"
    echo "  stop    - Stop the development server"
    echo "  restart - Restart the development server"
    echo "  status  - Check server status"
    echo "  logs n  - Show last n lines of logs (default: 50)"
    echo "  tail    - Follow log output"
    exit 1
    ;;
esac