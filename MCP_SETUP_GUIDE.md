# MCP (Model Context Protocol) Setup Guide for Claude Code

This guide explains how to set up MCP servers in this repository for automatic use with Claude Code.

## What is MCP?

MCP (Model Context Protocol) allows <PERSON> to interact with external tools and services through standardized server connections. This enables <PERSON> to perform actions like web browsing, database queries, file operations, and more.

## Prerequisites

- Node.js (v16 or higher)
- npm or yarn package manager
- <PERSON> (claude.ai/code)

## Setting Up MCP Configuration

### 1. Create MCP Configuration File

Create a `.mcp/config.json` file in your project root:

```json
{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "/path/to/allowed/directory"]
    },
    "git": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-git"]
    },
    "puppeteer": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-puppeteer"]
    },
    "postgres": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-postgres", "postgresql://user:pass@localhost/db"]
    }
  }
}
```

### 2. Alternative: Local MCP Server Installation

For better performance, install MCP servers locally:

```bash
# Install MCP servers as dev dependencies
npm install --save-dev @modelcontextprotocol/server-filesystem
npm install --save-dev @modelcontextprotocol/server-git
npm install --save-dev @modelcontextprotocol/server-puppeteer
npm install --save-dev @modelcontextprotocol/server-postgres
```

Then update `.mcp/config.json` to use local installations:

```json
{
  "mcpServers": {
    "filesystem": {
      "command": "node",
      "args": ["node_modules/@modelcontextprotocol/server-filesystem/dist/index.js", "./"]
    },
    "git": {
      "command": "node",
      "args": ["node_modules/@modelcontextprotocol/server-git/dist/index.js"]
    },
    "puppeteer": {
      "command": "node",
      "args": ["node_modules/@modelcontextprotocol/server-puppeteer/dist/index.js"]
    }
  }
}
```

### 3. Project-Specific Configuration

For this Tymbox landing page project, recommended MCP configuration:

```json
{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "./src", "./public", "./messages"]
    },
    "git": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-git"]
    },
    "puppeteer": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-puppeteer"],
      "env": {
        "PUPPETEER_HEADLESS": "true"
      }
    },
    "search": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-search"]
    }
  }
}
```

## Available MCP Servers

### 1. Shell Server (mcp-shell)
- **Purpose**: Execute shell commands including npm, node, and other CLI tools
- **Use cases**: 
  - Running npm dev server (`npm run dev`)
  - Executing build commands
  - Managing processes
  - Running tests
  - Any shell/terminal commands
- **Package**: `mcp-shell` by High Dimensional Research
- **Security**: Built-in security measures for safe command execution

### 2. Filesystem Server
- **Purpose**: Advanced file operations beyond Claude's built-in capabilities
- **Use cases**: Bulk file operations, complex directory traversal
- **Configuration**: Specify allowed directories for security

### 3. Git Server
- **Purpose**: Git operations without shell commands
- **Use cases**: Branch management, commit history analysis, git operations
- **Benefits**: Safer than shell git commands, structured output

### 4. Puppeteer Server
- **Purpose**: Web browser automation
- **Use cases**: 
  - Testing UI components
  - Capturing screenshots
  - Web scraping for research
  - Testing multi-domain functionality
- **Note**: Already connected in your setup

### 5. PostgreSQL Server
- **Purpose**: Database operations
- **Use cases**: If you add a database to the project
- **Configuration**: Requires database connection string

### 6. Search Server
- **Purpose**: Enhanced code search capabilities
- **Use cases**: Finding code patterns, refactoring

## Usage Examples

Once configured, Claude can automatically use these servers:

### Example 1: Running Development Server
```
"Use the shell server to run npm run dev and show me the output"
"Start the development server and monitor its logs"
"Run the dev server and check if both domains are working"
```

### Example 2: Testing Multi-Domain Setup
```
"Can you use Puppeteer to test that tymbox.cz shows Czech content and teambox.sk shows Slovak content?"
```

### Example 3: Git Operations
```
"Show me all commits related to internationalization using the Git server"
```

### Example 4: Bulk File Operations
```
"Using the filesystem server, find all TypeScript files that import from next-intl"
```

### Example 5: Combined Operations
```
"Use the shell server to start the dev server, then use Puppeteer to test the homepage"
```

## Security Considerations

1. **Limit filesystem access**: Only allow access to project directories
2. **Environment variables**: Store sensitive data (API keys, passwords) in `.env.local`
3. **Git ignore**: Add `.mcp/` to `.gitignore` if it contains sensitive configurations
4. **Database credentials**: Never commit database credentials

## Troubleshooting

### MCP Server Not Connecting
1. Check that the command path is correct
2. Verify Node.js is installed and in PATH
3. Try running the server command manually to see errors

### Permission Errors
- Ensure Claude has permission to access specified directories
- On Unix systems, check file permissions

### Server Crashes
- Check server logs for errors
- Ensure all dependencies are installed
- Verify configuration JSON is valid

## Adding to CLAUDE.md

Add this section to your CLAUDE.md file:

```markdown
## MCP Integration
- MCP servers are configured in `.mcp/config.json`
- Available servers: filesystem, git, puppeteer, search
- Use MCP servers for complex operations beyond built-in tools
- Puppeteer server available for browser automation and testing
```

## Next Steps

1. Create `.mcp/config.json` with your desired configuration
2. Test each server connection
3. Update CLAUDE.md with MCP usage guidelines
4. Consider adding project-specific MCP servers as needed

## Resources

- [MCP Documentation](https://modelcontextprotocol.io/docs)
- [MCP Server Registry](https://github.com/modelcontextprotocol/servers)
- [Creating Custom MCP Servers](https://modelcontextprotocol.io/docs/guides/creating-servers)